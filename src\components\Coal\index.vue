<template>
  <div class="coalContent">
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="自有煤源" name="NB">
        <super-table ref="coal" :actions="actions" :border="true" :customFilters="customFilters"
                     :defaultConfig="superTableConfigNB" :height="height" :list.sync="list" :listFunc="listFuncV"
                     :query="listQuery" :selectedRow.sync="selectedList" :showSelection="canChoose"
                     :span-method="spanMethod" istype="list" mountQuery name="coal" title="煤质数据库"
                     @clean="handleClean">
          <!-- <el-table-column label="品名" slot="name" width="100">
            <template slot-scope="scope">
              <div style="display: flex; position: relative">
                {{ scope.row['name'] }}
              </div>
            </template>
          </el-table-column> -->
          <!--          <el-table-column label="品名" slot="name" width="100" align="center">
                      <template slot-scope="scope">
                        <div v-if="scope.row['name']==='合计'"> {{ scope.row['name'] }}</div>
                        <div class="active-name" v-else style="position: relative" @click="editV(scope.row)">
                          <div v-if="scope.row.isArtificialUpdateIndicators==='Y'"
                               style="width: 40px; height: 37px; position: absolute; z-index: 10;background: #f89999; top: -20px; left: -26px;transform:rotate(45deg);">
                          </div>
                          <span v-if="scope.row.isArtificialUpdateIndicators==='Y'"
                                style="position: absolute; top: -6px; left: -4px;z-index: 99;font-size:12px;color: #fff;">人</span>
                          {{ scope.row['name'] }}
                          <div v-if="scope.row.isCoalRock==='Y'"
                               style="width: 40px; height: 37px; position: absolute; z-index: 10;background: #f8a20f; top: -20px; right: -25px; transform:rotate(45deg);">
                          </div>
                          <span v-if="scope.row.isCoalRock==='Y'"
                                style="position: absolute; top: -6px; right: -3px;z-index: 99; font-size: 12px;color: #fff">岩</span>
                        </div>
                      </template>
                    </el-table-column>-->
          <!-- <el-table-column label="反射率区间直方图" slot="chart" minWidth="100">
            <template slot-scope="scope">
              <div v-if="scope.row.isCoalRock==='Y'" @click="clickChart(scope.row)">
                <min-column-chart :id="scope.row.id" :impChartData="scope.row" :isShowLabel="false"
                                  style="width: 100px; height: 50px;"></min-column-chart>
              </div>
            </template>
          </el-table-column> -->
          <el-table-column label="矿井名称" align="left" prop="mineName">
            <template slot-scope="scope">
              {{ scope.row.mineName }}
            </template>
          </el-table-column>
          <el-table-column label="库存量" align="left" prop="stock">
            <template slot-scope="scope">
              <span style="color:#FF726B">{{ scope.row.stock }}</span>
            </template>
          </el-table-column>
          <el-table-column label="库存价" align="left" prop="price">
            <template slot-scope="scope">
              {{ scope.row.price }}
            </template>
          </el-table-column>
          <el-table-column label="水分" align="left" prop="mt">
            <template slot-scope="scope">
              {{ scope.row.mt }}
            </template>
          </el-table-column>
          <el-table-column label="灰分" align="left" prop="ad">
            <template slot-scope="scope">
              {{ scope.row.ad }}
            </template>
          </el-table-column>
          <el-table-column label="硫分" align="left" prop="std">
            <template slot-scope="scope">
              {{ scope.row.std }}
            </template>
          </el-table-column>
          <el-table-column label="挥发分" align="left" prop="vdaf">
            <template slot-scope="scope">
              {{ scope.row.vdaf }}
            </template>
          </el-table-column>
          <el-table-column label="G" align="left" prop="g">
            <template slot-scope="scope">
              {{ scope.row.g }}
            </template>
          </el-table-column>
          <el-table-column label="Y" align="left" prop="y">
            <template slot-scope="scope">
              {{ scope.row.y }}
            </template>
          </el-table-column>
          <el-table-column label="CRC" align="left" prop="crc">
            <template slot-scope="scope">
              {{ scope.row.crc }}
            </template>
          </el-table-column>
          <el-table-column label="反射率" align="left" prop="macR0">
            <template slot-scope="scope">
              {{ scope.row.macR0 }}
            </template>
          </el-table-column>
          <el-table-column label="标准差" align="left" prop="macS">
            <template slot-scope="scope">
              {{ scope.row.macS }}
            </template>
          </el-table-column>
          <el-table-column label="回收" align="left" prop="recovery">
            <template slot-scope="scope">
              {{ scope.row.recovery }}
            </template>
          </el-table-column>
          <el-table-column slot="operate" fixed="right" label="操作" width="80" align="center" v-if="canChoose">
            <template slot-scope="scope" v-if="scope.row.name!='合计'">
              <el-button @click="choose(scope.row.id)" type="primary" size="mini">选择</el-button>
            </template>
          </el-table-column>
        </super-table>
      </el-tab-pane>
      <el-tab-pane label="外部煤源" name="WB">
        <super-table ref="coal" :actions="actions" :border="true" :customFilters="customFilters"
                     :defaultConfig="superTableConfigWB" :height="height" :list.sync="list" :listFunc="listFunc"
                     :mountQuery="true" :query="listQuery" :selectedRow.sync="selectedList" :showSelection="canChoose"
                     name="coal" title="煤质数据库" @clean="handleClean">
          <!--          <el-table-column label="名称" slot="name" width="100">
                      <template slot-scope="scope">
                        <div class="active-name" style="display: flex; position: relative" @click="edit(scope.row)">
                          {{ scope.row['name'] }}
                          <div v-if="scope.row.isCoalRock==='Y'"
                               style="width: 40px; height: 40px; position: absolute; z-index: 10;background: #f8a20f; top: -20px; right: -25px; transform:rotate(45deg);">
                          </div>
                          <span v-if="scope.row.isCoalRock==='Y'"
                                style="position: absolute; top: -15px; right: -3px;z-index: 99; font-size: 12px;color: #fff">岩</span>
                        </div>
                      </template>
                    </el-table-column>-->
          <el-table-column label="创建时间" slot="createDate" width="90">
            <template slot-scope="scope">
              <div>{{ scope.row['createDate'].substr(0, 10) }}</div>
            </template>
          </el-table-column>
          <el-table-column label="煤种" slot="type" minWidth="60">
            <template slot-scope="scope">
              <state-tag v-model="scope.row.type" format="coal_type"></state-tag>
            </template>
          </el-table-column>
          <!--          <el-table-column label="反射率区间直方图" slot="chart" minWidth="100">
                      <template slot-scope="scope">
                        <div v-if="scope.row.isCoalRock==='Y'" @click="clickChart(scope.row)">
                          <min-column-chart :id="scope.row.id" :impChartData="scope.row" :isShowLabel="false"
                                            style="width: 100px; height: 50px;"></min-column-chart>
                        </div>
                      </template>
                    </el-table-column>-->
          <el-table-column label="省市区" slot="cityArea" width="120">
            <template slot-scope="scope">
              {{ scope.row.cityArea }}
            </template>
          </el-table-column>
          <el-table-column slot="operate" fixed="right" align="center" label="操作" width="200" v-if="!canChoose">
            <template slot-scope="scope">
              <div style="display:flex;align-items:center;justify-content: center;">
                <el-button @click="handleDelete(scope.row)" style="width:50px;height:30px;" type="delete">删除
                </el-button>
                <!--                        <el-button @click="evaluationReport(scope.row)" type="sendEmail"-->
                <!--                                   style="width:50px;height:30px;">评价-->
                <!--                        </el-button>-->
                <span class="btn" v-if="!canChoose&& scope.row.isFavorite === 'N'" @click="handleSetTop(scope.row,'Y')">置顶
                </span>
                <span class="btn" v-if="!canChoose&& scope.row.isFavorite === 'Y'" @click="handleSetTop(scope.row,'N')">取消置顶
                </span>
              </div>
            </template>
          </el-table-column>
          <el-table-column slot="operate" fixed="right" label="操作" width="80" align="center" v-if="canChoose">
            <template slot-scope="scope">
              <el-button @click="choose(scope.row.id)" type="primary" size="mini">选择</el-button>
            </template>
          </el-table-column>
        </super-table>
      </el-tab-pane>
      <el-tab-pane label="掌上煤焦煤源" name="ZS">
        <super-table ref="coal" :actions="actions" :border="true" :customFilters="customFilters"
                     :defaultConfig="superTableConfigZS" :height="height" :list.sync="list" :listFunc="listFuncZSMJ"
                     :mount-query="true" :query="listQuery" :selectedRow.sync="selectedList" :showSelection="canChoose"
                     :span-method="spanMethod" name="coal" title="煤质数据库" @clean="handleClean">
          <!--          <el-table-column label="名称" slot="name" width="100">
                      <template slot-scope="scope">
                        <div class="active-name" style="display: flex; position: relative" @click="edit(scope.row)">
                          {{ scope.row['name'] }}
                          <div v-if="scope.row.isCoalRock==='Y'"
                               style="width: 40px; height: 37px; position: absolute; z-index: 10;background: #f8a20f; top: -20px; right: -25px; transform:rotate(45deg);">
                          </div>
                          <span v-if="scope.row.isCoalRock==='Y'"
                                style="position: absolute; top: -4px; right: -3px;z-index: 99; font-size: 12px;color: #fff">岩</span>
                        </div>
                      </template>
                    </el-table-column>-->
          <el-table-column label="煤炭指标" slot="indexA" width="90" align="center">
            <el-table-column align="center" label="灰分Ad%" prop="cleanAd" width="70"/>
            <el-table-column align="center" label="硫分Std%" prop="cleanStd" width="70"/>
            <el-table-column align="center" label="挥发分Vdaf%" prop="cleanVdaf" width="90"/>
            <el-table-column align="center" label="粘结G" prop="procG" width="50"/>
            <el-table-column align="center" label="Y/mm" prop="procY" width="55"/>
            <el-table-column align="center" label="X/mm" prop="procX" width="55"/>
          </el-table-column>
          <el-table-column label="现行价" slot="price" width="90" align="center">
            <el-table-column align="center" label="含税煤价" prop="activePriceCoalPriceWithTax" width="65"/>
            <el-table-column align="center" label="涨跌" prop="rise" width="50"/>
            <el-table-column align="center" label="不含税煤价" prop="activePriceCoalPriceNoTax" width="80"/>
            <el-table-column align="center" label="不含税运费" prop="activePriceTransportPriceNoTax" width="80"/>
            <el-table-column align="center" label="路耗1%" prop="activePriceTransportCostP1" width="60"/>
            <el-table-column align="center" label="不含税进厂价" prop="activePriceFactoryPriceNoTax" width="90"/>
            <el-table-column align="center" label="水分%" prop="cleanMt" width="60"/>
            <el-table-column align="center" label="进厂干基成本" prop="activePriceFactorDryBasisCost" width="90"/>
            <el-table-column align="center" label="供应情况" prop="activePriceSupplyInfo" width="75"/>
          </el-table-column>
          <el-table-column label="煤炭指标" slot="indexB" width="90" align="center">
            <el-table-column align="center" label="MF/ddpm" prop="coalIndexMfddpm" width="75"/>
            <el-table-column align="center" label="奥亚膨胀b%" prop="coalIndexAypz" width="90"/>
            <el-table-column align="center" label="奥亚收缩a%" prop="coalIndexAyss" width="90"/>
            <el-table-column align="center" label="MCI%" prop="coalIndexMci" width="55"/>
          </el-table-column>
          <el-table-column label="焦炭指标" slot="indexC" width="90" align="center">
            <el-table-column align="center" label="煤焦比" prop="cokeIndexMjb" width="60"/>
            <el-table-column align="center" label="灰分" prop="cokeIndexAd" width="50"/>
            <el-table-column align="center" label="硫分" prop="cokeIndexStd" width="50"/>
            <el-table-column align="center" label="CRI%" prop="cokeIndexCri" width="50"/>
            <el-table-column align="center" label="CSR%" prop="cokeIndexCsr" width="55"/>
            <el-table-column align="center" label="M40" prop="cokeIndexM40" width="50"/>
            <el-table-column align="center" label="M10" prop="cokeIndexM10" width="50"/>
          </el-table-column>
          <el-table-column label="煤炭指标" slot="indexD" width="90" align="center">
            <el-table-column align="center" prop="macR0" label="反射率(R0)" width="80"/>
            <el-table-column align="center" prop="macS" label="标准差(S)" width="80"/>
            <el-table-column align="center" prop="macV" label="镜质组(V)" width="80"/>
            <el-table-column align="center" prop="macI" label="丝质组(I)" width="80"/>
            <el-table-column align="center" prop="macE" label="稳定组(E)" width="80"/>
            <el-table-column align="center" prop="active" label="活性物" width="80"/>
          </el-table-column>
          <el-table-column label="煤种" slot="type" minWidth="60">
            <template slot-scope="scope">
              <state-tag v-model="scope.row.type" format="coal_type"></state-tag>
            </template>
          </el-table-column>
          <el-table-column label="省市区" slot="cityArea" width="120">
            <template slot-scope="scope">
              {{ scope.row.cityArea }}
            </template>
          </el-table-column>
          <el-table-column slot="operate" fixed="right" label="操作" width="80" align="center" v-if="canChoose">
            <template slot-scope="scope" v-if="scope.row.name!='合计'">
              <el-button @click="choose(scope.row.id)" type="primary" size="mini">选择</el-button>
            </template>
          </el-table-column>
        </super-table>
      </el-tab-pane>
    </el-tabs>
    <el-dialog title="反射率直方图" :visible.sync="chartVisible" :before-close="closeDialog"
               :close-on-press-escape="false"
               :close-on-click-modal="false" append-to-body width="70%">
      <div class="dialog-layout">
        <min-column-chart :id="chartData.id" v-if="chartVisible" :impChartData="chartData" :isShowLabel="true"
                          style="width: 100%; height: 300px;"/>
      </div>
    </el-dialog>
    <el-dialog :title="dialogTitle" :visible.sync="dialogFormVisible" :before-close="closeDialog"
               :close-on-press-escape="false"
               :close-on-click-modal="false" append-to-body fullscreen>
      <div class="saveBtn">
        <el-button type="primary" :loading="entityFormLoading" @click="save">保存
        </el-button>
      </div>
      <coal-entity-form style="padding:10px 30px 30px 30px" :isAdd="isAdd" :entityForm.sync="entityForm"
                        ref="entityForm"
                        v-if="dialogFormVisible"></coal-entity-form>
    </el-dialog>
    <el-dialog title="煤种不一致" :visible.sync="isChangeCoal" width="80%" :before-close="handleClose">
      <div style="display: flex;font-size: 14px;">
        当前您选择的煤种是
        <span style="color: #A50D0F;font-size: 16px;">{{ entityForm.type }}</span>
        ,系统判断煤种是
        <span style="color: #A50D0F;font-size: 16px;">{{ type }}</span>
        ,是否修改煤种?
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="isChangeCoal = false">取 消</el-button>
        <el-button @click="makeSure('')" type="primary" :loading="entityFormLoading">直接保存</el-button>
        <el-button type="primary" @click="makeSure(type)" :loading="entityFormLoading">确定修改并保存</el-button>
      </span>
    </el-dialog>
    <AddCoal :add-visible="addVisible" :details="coalDetail" @closeVisible="handleAddClose"
             :isSourceWash="true"></AddCoal>

    <addCoalzy :add-visible="addVisibleV" :details="coalDetailV" @closeVisible="handleAddClose" :isSourceWash="true"
               style="z-index:1999">
    </addCoalzy>
  </div>
</template>

<script>
import Func from '@/utils/func'
import {addCoal, addCoals, addRockCoala} from '@/api/workspace'

import {
  deleteCoalById,
  pageCoal,
  saveCoal,
  isCheckCoal,
  copyToAssay,
  copyToResource,
  locationMap,
  setFavorite,
  deleteSomeCoalById,
  pageStock, pageByCoalCategory
} from '@/api/coal'

import AddCoal from './addCoal'
import addCoalzy from './addCoalzy'
import RegionSelect from '@/components/RegionSelect'
import {dateFormat} from '@/filters'
import CoalEntityForm from '@/components/CoalEntityForm'
import ColumnChart from '@/components/Chart/columnChart'
import MinColumnChart from '@/components/Chart/minColumnChart'
import StateTag from '@/components/StateTag'
import DateRanger from '@/components/DateRanger'
import {addCoalWashing} from '@/api/coalWashing'
import SuperTable from '@/components/SuperTable/index.vue'

// 实体模型
const entityForm = {
  id: '',
  name: '',
  batchCode: dateFormat(new Date(), 'yyyymmdd'),
  type: '', // 默认焦煤
  province: '',
  factoryPrice: '',
  transitFee: '',
  roadCost: '',
  arrivePrice: '',
  arriveFactory: '',
  mineDepth: '',
  rawMt: '',
  rawAd: '',
  rawPointFive: '',
  rawOnePointFour: '',
  rawAdIn: '',
  rawStd: '',
  rawVdaf: '',
  rawG: '',
  cleanVdaf: '',
  cleanAd: '',
  cleanStd: '',
  cleanMt: '',
  cleanP: '',
  procG: '',
  procY: '',
  procX: '',
  procMf: '',
  procTp: '',
  procTmax: '',
  procTk: '',
  procCrc: '',
  procA: '',
  procB: '',
  macR0: '',
  macS: '',
  macV: '',
  macI: '',
  macE: '',
  comSiO2: '',
  comAl2O3: '',
  comFe2O3: '',
  comCaO: '',
  comMgO: '',
  comNa2O: '',
  comK2O: '',
  comTiO2: '',
  comP2O5: '',
  comSO3: '',
  cfeCp: '',
  cfeCe: '',
  qualScon: '',
  qualPcon: '',
  qualM40: '',
  qualM10: '',
  qualCsr: '',
  qualCri: '',
  qualTestCond: '',
  crGk: '',
  crBk: '',
  crTk: '',
  city: '',
  dataBelong: '',
  dataType: '',
  createBy: '',
  createDate: '',
  updateBy: '',
  updateDate: '',
  remarks: '',
  ext: '',
  location: '',
  attachment: '',
  attachmentName: '',
  isFavorite: 'N'
}
export default {
  name: 'Coal',
  components: {
    SuperTable,
    RegionSelect,
    CoalEntityForm,
    StateTag,
    ColumnChart,
    MinColumnChart,
    DateRanger,
    AddCoal,
    addCoalzy
  },
  data() {
    return {
      coalDetail: {},
      coalDetailV: {},
      addVisibleV: false,
      addVisible: false,
      isLoading: false,
      // 超级表格配置
      chartVisible: false,
      name: 'coal',
      listFunc: pageCoal,
      listFuncV: pageStock,
      listFuncZSMJ: pageByCoalCategory,
      space: {},
      printData: {},
      listQuery: {
        orderBy: 'createDate',
        orderDir: 'desc'
      },
      type: '',
      superTableConfigWB: {
        columns: [
          {
            slot: 'index',
            isShow: true
          },
          {
            prop: 'name',
            label: '名称',
            isShow: true,
            isFilter: true,
            filter: {
              prop: 'filter_LIKES_name',
              label: '名称'
            },
            width: 120
          },
          {
            prop: 'location',
            label: '产地',
            isShow: true,
            isFilter: true,
            width: 140
          },
          {
            slot: 'chart',
            label: '图形',
            isShow: true,
            minWidth: 100
          },
          {
            prop: 'type',
            // slot: 'type',
            label: '煤种',
            isShow: true,
            minWidth: 60,
            isFilter: true,
            filter: {
              prop: 'filter_EQS_type',
              label: '煤种',
              component: 'DictSelect',
              type: 'coal_type'
            }
          },

          {
            prop: 'cleanAd',
            label: 'Ad',
            sortable: true,
            minWidth: 50,
            isShow: true
          },
          {
            prop: 'cleanVdaf',
            label: 'Vdaf',
            minWidth: 60,
            sortable: true,
            isShow: true
          },
          {
            prop: 'cleanStd',
            label: 'St,d',
            sortable: true,
            minWidth: 60,
            isShow: true
          },
          {
            prop: 'cleanP',
            label: 'P',
            sortable: true,
            minWidth: 50,
            isShow: false
          },
          {
            prop: 'procG',
            label: 'G',
            sortable: true,
            minWidth: 50,
            isShow: true
          },
          {
            prop: 'procY',
            label: 'Y',
            sortable: true,
            minWidth: 50,
            isShow: true
          },
          {
            prop: 'macR0',
            label: '反射率(R0)',
            minWidth: 70,
            isShow: true
          },
          {
            prop: 'macS',
            label: '标准差(S)',
            minWidth: 70,
            isShow: true
          },
          // {
          //   prop: 'qualScon',
          //   label: '硫转化率',
          //   minWidth: 60,
          //   isShow: true
          // },
          {
            prop: 'qualCsr',
            minWidth: 40,
            label: 'CSR',
            isShow: true
          },
          {
            prop: 'cleanMt',
            label: 'Mt',
            minWidth: 50,
            sortable: true,
            isShow: true
          },
          {
            prop: 'arrivePrice',
            label: '到厂价',
            minWidth: 60,
            isShow: true
          },
          {
            prop: 'createDate',
            slot: 'createDate',
            label: '创建时间',
            isShow: true,
            width: 100
          },
          {
            prop: 'remarks',
            label: '备注',
            isShow: false,
            minWidth: 140,
            showOverflowTooltip: true
          }
        ]
      },
      superTableConfigNB: {
        columns: [
          {
            slot: 'index',
            isShow: true
          },
          {
            prop: 'coalCategory',
            label: '煤种',
            isShow: true,
            align: 'center',
            width: 80
          },
          {
            prop: 'name',
            label: '品名',
            isShow: true,
            isFilter: true,
            filter: {
              prop: 'filter_LIKES_name',
              label: '品名'
            },
            width: 120
          },
          // {
          //   slot: 'chart',
          //   label: '图形',
          //   isShow: true,
          //   minWidth: 100
          // },
          {
            prop: 'mineName',
            label: '矿井名称',
            isShow: true
          },
          {
            prop: 'stock',
            label: '库存量',
            isShow: true
          },
          {
            prop: 'price',
            label: '库存价',
            isShow: true
          },

          {
            prop: 'ad',
            label: '灰分',
            isShow: true
          },
          {
            prop: 'std',
            label: '硫分',
            isShow: true
          },
          {
            prop: 'vdaf',
            label: '挥发分',
            isShow: true
          },
          {
            prop: 'g',
            label: 'G',
            isShow: true
          },
          {
            prop: 'y',
            label: 'Y',
            isShow: true
          },
          {
            prop: 'crc',
            label: 'CRC',
            isShow: true
          },
          {
            prop: 'macR0',
            label: '反射率',
            isShow: true
          },
          {
            prop: 'macS',
            label: '标准差',
            isShow: true
          },
          {
            prop: 'mt',
            label: '水分',
            isShow: true
          },
          {
            prop: 'recovery',
            label: '回收',
            isShow: true
          }
          // {
          //   prop: 'type',
          //   label: '煤种',
          //   isShow: true,
          //   minWidth: 60,
          //   isFilter: true,
          //   filter: {
          //     prop: 'filter_EQS_type',
          //     label: '煤种',
          //     component: 'DictSelect',
          //     type: 'coal_type',
          //   },
          // },
        ]
      },
      superTableConfigZS: {
        columns: [
          {
            slot: 'index',
            isShow: true
          },
          {
            prop: 'date',
            label: '更新日期',
            isShow: true,
            width: 120
          },
          {
            label: '煤种名称',
            prop: 'coalCategoryName',
            isShow: true,
            width: 100
          },
          {
            label: '产地',
            prop: 'area',
            isShow: true,
            width: 80
          },
          {
            label: '品名简称',
            prop: 'coalShortname',
            isShow: true,
            width: 90
          },
          {
            label: '煤炭指标',
            isShow: true,
            slot: 'indexA'
          },
          {
            label: '现行价',
            isShow: true,
            slot: 'price'
          },
          {
            label: '煤炭指标',
            isShow: true,
            slot: 'indexB'
          },
          {
            label: '焦炭指标',
            isShow: true,
            slot: 'indexC'
          },
          {
            label: '煤炭指标',
            isShow: true,
            slot: 'indexD'
          },
          {
            label: '回收率%',
            isShow: true,
            prop: 'recoveryRate',
            width: 80
          },
          {
            label: '分类',
            isShow: true,
            prop: 'categoryType',
            width: 80
          },
          {
            label: '内部煤种',
            isShow: true,
            prop: 'insideCoalCategory',
            width: 80
          },
          {prop: 'activeInertiaRatio', label: '活惰比', width: '50'}
        ]
      },

      selectedList: [],
      region: [],
      // 编辑配置
      entityForm: Object.assign({}, entityForm),
      entityFormRules: {},
      entityFormLoading: false,
      dialogFormVisible: false,
      contrastVisible: false,
      dialogMapVisible: false,
      checkBoxGather: {},
      getCityAreaText: '',
      multChooseLoading: false, // 防抖动
      mapFactoryList: [],
      mapCoalList: [],
      isChangeCoal: false,
      list: [],
      chartData: [],
      isAdd: null,
      customFilters: [],
      activeName: 'WB'
    }
  },
  props: {
    datatype: {
      // 数据类型
      type: [String],
      default: 'HY'
    },
    canChoose: {
      // 是否选煤
      type: [Boolean],
      default: false
    },
    isShowCheckBox: {
      // 是否显示复选框
      type: [Boolean],
      default: true
    },
    height: {
      // 数据类型
      type: [Number],
      default: 600
    },
    permissionPrefix: {
      // 权限前缀
      type: [String]
    }
  },
  mounted() {
    console.log(this.canChoose)
    if (this.canChoose) {
      this.name = 'choose_coal'
      this.superTableConfigNB.columns.push({
        slot: 'operate',
        label: '操作',
        isShow: true
      })
      this.superTableConfigWB.columns.push({
        slot: 'operate',
        label: '操作',
        isShow: true


      })
    } else {
      this.superTableConfigNB.columns.push({
        slot: 'operate',
        label: '操作',
        isShow: true
      })
      this.superTableConfigWB.columns.push({
        slot: 'operate',
        label: '操作',
        isShow: true
      })
    }
  },
  created() {
    // this.customFilters.push({
    //   component: 'DataTypeSelect',
    //   prop: 'filter_EQS_datatype',
    //   label: '来源',
    // })
    this.selectedList = []
    // this.$nextTick(() => {
    //   console.log(this.$refs.coal)
    // })

    // this.$refs.coal.selectedList = []

    this.activeTitle = 'MWASH'

    // this.activeTitle = this.$route.query.activeTitle
  },
  computed: {
    dialogTitle() {
      return this.entityForm.id ? this.entityForm.name : '新增'
    },
    actions() {
      return !this.canChoose
        ? [
          {
            config: {
              type: 'add'
            },
            label: '新增',
            click: this.add,
            params: {
              type: 'add'
            }
          }
        ]
        : [
          {
            config: {
              type: 'primary'
            },
            label: '选煤',
            click: this.multChoose,
            params: {
              type: 'add'
            }
          }
        ]
    }
  },
  watch: {
    region(val) {
      this.entityForm = Object.assign(this.entityForm, {
        province: val[0],
        city: val[1],
        area: val[2]
      })
    },
    async $route(to, from) {
      if (to.path === '/system/coal') {
        this.getList()
      }
    },
    list(v) {
      this.$emit('empty', v.length)
    }
  },
  methods: {
    spanMethod({row, column, rowIndex, columnIndex}, datalsit) {
      if (columnIndex === 1) {
        let spanArr = this.getSpanArr(datalsit)
        // 页面列表上 表格合并行 -> 第几列(从0开始)
        // 需要合并多个单元格时 依次增加判断条件即可
        // 数组存储的数据 取出
        const _row = spanArr[rowIndex]
        const _col = _row > 0 ? 1 : 0
        return {
          rowspan: _row,
          colspan: _col
        }
      } else {
        //不可以return {rowspan：0， colspan: 0} 会造成数据不渲染， 也可以不写else，eslint过不了的话就返回false
        return false
      }
    },
    getSpanArr: function (data) {
      let spanArr = []
      let pos = ''
      for (let i = 0; i < data.length; i++) {
        if (i === 0) {
          spanArr.push(1)
          pos = 0
        } else {
          // 判断当前元素与上一个元素是否相同
          if (data[i].coalCategory === data[i - 1].coalCategory || data[i].name == '合计') {
            spanArr[pos] += 1
            spanArr.push(0)
          } else {
            spanArr.push(1)
            pos = i
          }
        }
      }
      return spanArr
    },

    handleClick(tab) {
      this.selectedList = []
      this.activeName = tab.name
      this.getList()
    },
    handleAddClose({isSave}) {
      this.coalDetail = {}
      this.coalDetailV = {}
      this.addVisible = false
      this.addVisibleV = false
      this.getList()
    },
    // 重置数据
    handleClean(e) {
      // this.getList(beginDate)
    },
    async someDelete() {
      const ids = []
      this.selectedList.forEach((item) => {
        ids.push(item.id)
      })
      await this.$confirm('即将删除该条记录，是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      this.isLoading = true
      const res = await Func.fetch(deleteSomeCoalById, {ids})
      if (res.data.length > 0) {
        let str = res.data.join(',')
        this.$message.warning(str + `在配煤或配煤方案中使用不允许删除`)
      }
      this.isLoading = false
      this.getList()
    },
    clickChart(row) {
      this.chartVisible = true
      this.chartData = {...row}
    },
    // 评价
    evaluationReport(row) {
      const data = JSON.stringify(row)
      this.$router.push({path: '/evaluate', query: {data, isShowBack: true}})
    },
    // 置顶
    async handleSetTop(row, type) {
      const res = await Func.fetch(setFavorite, {id: row.id, isFavorite: type})
      if (res.data) {
        if (type === 'Y') {
          this.$message.success('置顶成功')
        } else {
          this.$message.success('已取消置顶')
        }
      }
      this.getList()
    },
    /*
     * 多选事件触发
     * */
    async multChoose() {
      const idList = []
      console.log(this.selectedList)
      if (!this.selectedList.length) {
        this.$message({
          message: '至少选择一条记录',
          type: 'warning'
        })
        return false
      }
      if (this.selectedList.length > 0) {
        this.selectedList.forEach((item) => {
          idList.push(item.id)
        })
      }
      if (this.list.length === 0) {
        this.$message.warning('您还没有煤源，马上去我的数据-自选煤源中添加煤源吧')
        return true
      }
      switch (this.activeTitle) {
        case 'COKE':
          const res1 = await Func.fetch(addCoals, {spaceType: 'YH', coalIdList: idList, source: this.activeName})
          if (res1) {
            this.$router.push('./autoBlending?activeTitle=COKE')
          }
          break
        case 'SWASH':
          const res2 = await Func.fetch(addRockCoal, {spaceType: 'YH', coalIdList: idList, source: this.activeName})
          if (res2) {
            this.$router.push('./autoBlending?activeTitle=SWASH')
          }
          break
        case 'MWASH':
          const res3 = await Func.fetch(addCoalWashing, {spaceType: 'YH', coalIdList: idList, source: this.activeName})
          if (res3) {
            this.$emit('closeChoose')
            // this.$router.push('./autoBlending?activeTitle=MWASH')
          }
          break
      }
      // this.$emit('multChoose', {idList, activeTitle: this.activeTitle})
    },
    /*
     * 单选事件触发
     * */
    // choose(id) {
    //     this.$emit('choose', {id, type: this.type})
    // },
    async choose(id) {
      // this.$emit('choose', {id, type: this.type})
      switch (this.activeTitle) {
        case 'COKE':
          let res1 = await Func.fetch(addCoal, {spaceType: 'YH', coalId: id, source: this.activeName})
          if (res1) {
            this.$router.push('./autoBlending?activeTitle=COKE')
          }
          break
        case 'SWASH':
          let res2 = await Func.fetch(addRockCoal, {spaceType: 'YH', coalIdList: [id], source: this.activeName})
          if (res2) {
            this.$router.push('./autoBlending?activeTitle=SWASH')
          }
          break
        case 'MWASH':
          let res3 = await Func.fetch(addCoalWashing, {spaceType: 'YH', coalIdList: [id], source: this.activeName})
          if (res3) {
            this.$emit('closeChoose')
            // this.$router.push('./autoBlending?activeTitle=MWASH')
          }
          break
      }
    },
    // 删除
    async handleDelete(row) {
      await this.$confirm('即将删除该条记录，是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      this.isLoading = true
      await Func.fetch(deleteCoalById, row.id)
      this.isLoading = false
      this.getList()
    },
    /**
     * 刷新列表
     */
    async getList() {
      // this.$refs['coal'].$emit('refresh')
      let data = {
        current: 1,
        size: 50,
        orderBy: 'createDate',
        orderDir: 'desc'
      }
      if (this.activeName == 'WB') {
        const res = await Func.fetch(pageCoal, {...data})
      } else if (this.activeName == 'NB') {
        const res = await Func.fetch(pageStock, {...data})
      } else if (this.activeName == 'ZSMJ') {
        const res = await Func.fetch(pageByCoalCategory(), {...data})
      }
    },
    /**
     * 新增
     */
    async add() {
      this.addVisible = true
      // this.$router.push({name: 'myDetails', params: {title: '新增', current: 'CoalDetails'}})
    },

    // 对比
    contrast() {
      if (!this.selectedList.length) {
        this.$message({
          message: '至少选择一条记录',
          type: 'warning'
        })
        return false
      }
      this.selectedList.map((item) => {
        item.isShow = false
        return item
      })
      this.contrastVisible = true
    },
    /**
     * 修改
     * @param row
     * @returns {Promise<void>}
     */
    async edit(row) {
      this.coalDetail = row
      this.add()

      // this.$emit('editCoal', {row})
      // this.$router.push({
      //     name: 'myDetails',
      //     params: {row, title: row.name, current: 'CoalDetails'}
      // })
    },

    async editV(row) {
      this.coalDetailV = row
      this.addVisibleV = true
    },

    /**
     * 删除
     * @param row
     */
    remove(row) {
      this.currentRow = row
      Func.deleteEntityById({
        context: this,
        impl: deleteCoalById,
        id: this.currentRow.id
      })
      this.getList()
    },
    /**
     * 保存表单
     * @param formName
     * @returns {Promise<void>}
     */
    async save() {
      const valid = await this.$refs.entityForm.validate()
      if (valid) {
        const res = await Func.fetch(isCheckCoal, {
          g: this.entityForm.procG,
          vdaf: this.entityForm.cleanVdaf,
          y: this.entityForm.procY
        })
        if (res.data) {
          if (res.data.name !== this.entityForm.type) {
            this.isChangeCoal = true
            this.type = res.data.name
          } else {
            this.entityFormLoading = true
            const entityFormData = {...this.entityForm, dataType: this.datatype}
            const saveRes = await Func.fetch(saveCoal, entityFormData)
            this.entityFormLoading = false
            if (saveRes.data) {
              this.$message({showClose: true, message: '提交成功', type: 'success'})
              this.getList()
              this.closeDialog()
            }
          }
        }
      }
    },

    async makeSure(coalType) {
      const {arriveFactory} = this.$refs.entityForm
      this.entityForm.type = coalType || this.entityForm.type
      this.entityFormLoading = true
      const entityFormData = {...this.entityForm, dataType: this.datatype, arriveFactory}
      const saveRes = await Func.fetch(saveCoal, entityFormData)
      this.entityFormLoading = false
      if (saveRes.data) {
        this.$message({showClose: true, message: '提交成功', type: 'success'})
        this.getList()
        this.closeDialog()
      }
    },
    /**
     * 关闭对话框
     */
    closeDialog() {
      this.dialogFormVisible = false
      this.chartVisible = false
      this.entityForm = {...entityForm}
      this.isChangeCoal = false
      // this.$refs.entityForm.$children[0].resetFields()
    },
    closeContrast() {
      this.contrastVisible = false
    },
    handleClose(done) {
      done()
    },
    async map() {
      const res = await Func.fetch(locationMap, {})
      if (res) {
        this.mapFactoryList = res.data.records
      }
      this.mapCoalList = this.list
      this.dialogMapVisible = true
    },
    /**
     * 拷贝到煤质数据库
     */
    async copyToResource() {
      const res = await Func.fetch(copyToResource, this.entityForm.id)
      if (res) {
        this.$message.success('拷贝成功，请至煤质数据库查看')
      }
    },
    /**
     * 拷贝到化验系统
     */
    async copyToAssay() {
      const res = await Func.fetch(copyToAssay, this.entityForm.id)
      if (res) {
        this.$message.success('拷贝成功，请至化验系统查看')
      }
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-tabs__header {
  margin: 0 0 0px;
}

::v-deep .el-dialog__header {
  border-radius: 4px 4px 0 0 !important;
}

::v-deep .el-dialog__body {
  height: 380px;
}

.dialog-layout {
  height: 100%;
  display: flex;
  align-items: center;
}

.saveBtn {
  padding-top: 10px;
  display: flex;
  justify-content: flex-end;
}

.btn {
  color: #ff6b57;
  padding-left: 10px;
}

.active-name {
  text-decoration: underline;
}
</style>
