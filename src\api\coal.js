import fetch from '@/utils/request'
import fetchPdf from '@/utils/fetchPdf'
// import store from '@/store'

const urlPrefix = '/cwe/a/coal'

export function getCoalReport(searchForm) {
  return fetch({
    url: 'a/coal/get',
    method: 'get',
    params: searchForm
  })
}

export function pageCoal(searchForm) {
  return fetch({
    url: urlPrefix + '/page',
    method: 'get',
    params: searchForm
  })
}

export function pageByCoalCategory(searchForm) {
  return fetch({
    url: '/cwe/a/coalSourceOutside/pageByCoalCategory',
    method: 'get',
    params: searchForm
  })
}

export function deleteCoalById(id) {
  return fetch({
    url: urlPrefix + '/delete',
    method: 'post',
    data: {id: id}
  })
}

export function deleteSomeCoalById(data) {
  return fetch({
    url: urlPrefix + '/batchDelete2',
    method: 'post',
    data: {...data}
  })
}

// 获取煤岩区间 a/coalRock/findRangeList
export function getRangeListApi() {
  return fetch({
    url: '/cwe/a/coalRock/findRangeList',
    method: 'get',
    data: {}
  })
}

export function deleteReport(id) {
  return fetch({
    url: '/cwe/a/reviewCoalLog/delete',
    method: 'post',
    data: {id: id}
  })
}

export function saveCoal(coal) {
  return fetch({
    url: urlPrefix + '/saveCoal',
    method: 'post',
    data: coal
  })
}

/**
 * 获取煤灰成份参数配置列表
 * @param searchForm
 */
export function coalAshList(location) {
  return fetch({
    url: '/cwe/a/coalAsh/page',
    method: 'get',
    params: location
  })
}

export function getFormState(id) {
  return fetch({
    url: '/cwe/a/sampleAssayApply/needManagerReview',
    method: 'get',
    params: {id}
  })
}

/**
 * 拷贝数据到化验系统
 * @param id
 */
export function copyToAssay(id) {
  return fetch({
    url: urlPrefix + '/copyToAssay',
    method: 'post',
    data: {id: id}
  })
}

/**
 * 拷贝数据到煤质数据库
 * @param id
 */
export function copyToResource(id) {
  return fetch({
    url: urlPrefix + '/copyToResource',
    method: 'post',
    data: {id: id}
  })
}

export function locationMap(filter) {
  return fetch({
    url: '/cwe/a/factory/page',
    method: 'get',
    params: filter
  })
}

/**
 * 保存上传的附件
 * @param data
 */
export function saveAttachment(data) {
  return fetch({
    url: urlPrefix + '/saveAttachment',
    method: 'post',
    data: data
  })
}

export function getResult(data) {
  return fetch({
    url: '/cwe/a/coalIndexScore/getResult',
    method: 'get',
    params: {...data}
  })
}

export function getBasicReport(data) {
  return fetch({
    url: '/cwe/a/coalIndexScore/getBasicReport',
    method: 'post',
    data: {...data}
  })
}

export function getPDFReport(data) {
  return fetchPdf({
    url: '/cwe/a/coalIndexScore/getPDFReport',
    method: 'get',
    params: {...data}
  })
}

export function saveBasicReport(data) {
  return fetch({
    url: '/cwe/a/coalIndexScore/saveBasicReport',
    method: 'post',
    data: {...data}
  })
}

export function saveDetailReport(data) {
  return fetch({
    url: '/cwe/a/coalIndexScore/saveDetailedReport',
    method: 'post',
    data: {...data}
  })
}

export function getReport(data) {
  return fetch({
    url: '/cwe/a/reviewCoalLog/get',
    method: 'get',
    params: {...data}
  })
}

export function getReviewReport(data) {
  return fetch({
    url: '/cwe/a/reviewCoalLog/getReport',
    method: 'get',
    params: {...data}
  })
}

export function getType(data) {
  return fetch({
    url: '/cwe/a/coal/getType',
    method: 'get',
    params: {...data}
  })
}

export function getDetailedReport(data) {
  return fetch({
    url: '/cwe/a/coalIndexScore/getDetailedReport',
    method: 'post',
    data: {...data}
  })
}

export function sendEmail(data) {
  return fetch({
    url: '/cwe/a/coalIndexScore/sendEmail',
    method: 'post',
    data: {...data}
  })
}

export function reviewDownoadReport(data) {
  return fetchPdf({
    url: '/cwe/a/reviewCoalLog/downoadReport',
    method: 'get',
    params: {...data}
  })
}

export function addForm(data) {
  return fetch({
    url: '/cwe/a/coal/addFromReview',
    method: 'post',
    data: {...data}
  })
}

export function getCoalById(data) {
  return fetch({
    url: '/cwe/a/coal/getCoalById',
    method: 'get',
    params: {...data}
  })
}

export function isCheckCoal(data) {
  return fetch({
    url: '/cwe/a/coal/getType',
    method: 'get',
    params: {...data}
  })
}

export function setFavorite(data) {
  return fetch({
    url: '/cwe/a/coal/setFavorite',
    method: 'post',
    data: {...data}
  })
}

export function findByType(data) {
  return fetch({
    url: '/cwe/a/cokeCostProvince/findByType',
    method: 'get',
    params: {...data}
  })
}

export async function pageStock(searchForm) {
  searchForm.filter_GTI_stock = 0
  const res = await fetch({
    // url: '/cwe/a/stock/page',
    url: '/cwe/a/stock/list',
    method: 'get',
    params: searchForm
  })
  // const typeName = {
  //     YM: '原煤',
  //     BCPJM: '半成品精煤',
  //     CPJM: '成品精煤',
  //     ZM: '中煤',
  //     GS: '矸石',
  //     JT: '焦炭'
  // }
  // res.data.forEach(item => {
  //     item.typeName = typeName[item.type]
  // })
  res.data = formatData(res.data)
  return res
}

function formatData(records) {
  if (records.length > 0) {
    let newArr = records.reduce((pre, cur) => {
      // console.log(cur)
      if (pre.length) {
        let index = pre.findIndex(item => item[0].coalCategory == cur.coalCategory)
        // console.log(index)
        if (index > -1) {
          pre[index].push(cur)
        } else {
          pre.push([cur])
        }
      } else {
        pre.push([cur])
      }
      return pre
    }, [])
    // console.log(newArr)
    newArr.forEach(item => {
      let stocktotal = 0
      // let pricetotal = 0;
      // let mttotal = 0;
      // let adtotal = 0;
      // let stdtotal = 0;
      // let vdaftotal = 0;
      // let gtotal = 0;
      // let ytotal = 0;
      // let crctotal = 0;
      // let macR0total = 0;
      // let macStotal = 0;
      // let recoverytotal = 0;
      item.forEach((item2) => {
        stocktotal += Number(item2.stock)
        // pricetotal += Number(item2.price)
        // mttotal += Number(item2.mt)
        // adtotal += Number(item2.ad)
        // stdtotal += Number(item2.std)
        // vdaftotal += Number(item2.vdaf)
        // gtotal += Number(item2.g)
        // ytotal += Number(item2.y)
        // crctotal += Number(item2.crc)
        // macR0total += Number(item2.macR0)
        // macStotal += Number(item2.macS)
        // recoverytotal += Number(item2.recovery)
      })
      let obj = {
        name: '合计',
        sequestrationFlag: '1',
        mineName: '',//矿井名称
        stock: stocktotal.toFixed(2)//库存量
        // price: pricetotal.toFixed(2),//库存价
        // mt: mttotal.toFixed(2),//Mt
        // ad: adtotal.toFixed(2),//Ad
        // std: stdtotal.toFixed(2),//St,d
        // vdaf: vdaftotal.toFixed(2),//Vdaf
        // g: gtotal.toFixed(2),//G
        // y: ytotal.toFixed(2),//Y
        // crc: crctotal.toFixed(2),//CRC
        // macR0: macR0total.toFixed(2),//反射率
        // macS: macStotal.toFixed(2),//标准差
        // recovery: recoverytotal.toFixed(2),//回收
      }
      item.push(obj)
    })
    // console.log(newArr)
    let subArr = newArr.reduce((pre, cur) => {
      return pre.concat(cur)
    }, [])
    return subArr
  }
}


export function getDetail(data) {
  return fetch({
    url: '/cwe/a/stock/getDetail',
    method: 'get',
    params: {...data}
  })
}

export function updateStock(data) {
  return fetch({
    url: '/cwe/a/stock/updateStock',
    method: 'post',
    data: {...data}
  })
}

export function refreshIndicators(filter) {
  return fetch({
    url: '/cwe/a/stock/refreshIndicators',
    method: 'post',
    data: filter
  })
}

export function refreshPrice(filter) {
  return fetch({
    url: '/cwe/a/stock/refreshPrice',
    method: 'post',
    data: filter
  })
}
