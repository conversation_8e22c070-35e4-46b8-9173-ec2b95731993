{"name": "m6-admin", "version": "0.1.0", "description": "m6-admin basing on vue-element-admin 3.9.3", "author": "linlinjava <<EMAIL>>", "license": "Apache-2.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "build:dep": "cross-env NODE_ENV=production env_config=dep node build/build.js", "build:prod": "cross-env NODE_ENV=production vue-cli-service build --mode production", "build:test": "cross-env NODE_ENV=production vue-cli-service build --mode test", "lint": "eslint --ext .js,.vue src", "test": "npm run lint", "precommit": "lint-staged", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml"}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "dependencies": {"@babel/plugin-transform-runtime": "^7.9.6", "@handsontable/vue": "^12.3.1", "@riophae/vue-treeselect": "0.0.37", "@vue/babel-helper-vue-jsx-merge-props": "^1.0.0", "@vue/babel-preset-app": "^4.3.1", "@vue/babel-preset-jsx": "^1.1.2", "ali-oss": "^6.2.1", "amap-js": "^2.4.0", "awe-dnd": "^0.3.4", "axios": "0.18.0", "clipboard": "^1.7.1", "connect": "3.6.6", "cos-nodejs-sdk-v5": "^2.5.14", "css-loader": "^3.2.0", "dayjs": "^1.11.10", "decimal.js": "^10.4.3", "dexie": "^2.0.4", "echarts": "^4.5.0", "element-resize-detector": "^1.1.15", "element-ui": "2.15.6", "exceljs": "^4.3.0", "file-saver": "^1.3.8", "font-awesome": "4.7.0", "handsontable": "^12.3.1", "js-cookie": "2.2.0", "js-pinyin": "^0.1.9", "jsbarcode": "^3.11.4", "jszip": "3.1.5", "lodash": "^4.17.21", "mathjs": "^12.4.1", "md5": "^2.2.1", "normalize.css": "7.0.0", "nprogress": "0.2.0", "path-to-regexp": "^3.1.0", "pdfmake": "^0.1.37", "print-js": "^1.6.0", "qrcode": "1.3.0", "qs": "^6.9.1", "screenfull": "3.3.3", "throttle-debounce": "^5.0.0", "tinymce": "^5.1.2", "v-charts": "1.19.0", "vue": "^2.6.10", "vue-amap": "^0.5.10", "vue-count-to": "^1.0.13", "vue-handsontable-official": "1.0.0", "vue-i18n": "7.3.2", "vue-jsonp": "^0.1.8", "vue-router": "^3.1.3", "vuedraggable": "^2.24.3", "vuex": "^3.1.1", "xlsx": "^0.11.19", "ysshandsontable-multi-select": "^1.0.2"}, "devDependencies": {"@babel/helper-module-imports": "^7.0.0", "@vue/cli-plugin-babel": "^3.6.0", "@vue/cli-plugin-pwa": "^3.6.0", "@vue/cli-plugin-unit-mocha": "^3.6.0", "@vue/cli-service": "^3.6.0", "@vue/test-utils": "^1.0.0-beta.20", "babel-plugin-jsx-v-model": "^2.0.3", "chai": "^4.1.2", "cross-env": "^7.0.3", "gulp": "^4.0.2", "gulp-ssh": "^0.7.0", "lint-staged": "^7.2.2", "sass": "^1.22.12", "sass-loader": "^7.0.1", "script-loader": "^0.7.2", "vue-template-compiler": "^2.5.17", "vuepress": "^0.14.3", "webpack": "^4.40.2"}, "volta": {"node": "12.22.12"}}