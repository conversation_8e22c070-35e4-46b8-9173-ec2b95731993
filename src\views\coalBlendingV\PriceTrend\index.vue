<template>
  <div class="app-container">
    <div class="content">
      <div v-for="(item, index) in list" :key="item.id" class="block">
        <div class="header">
          <div class="title">{{ item.name }}</div>
          <el-date-picker v-model="item.daterange" :picker-options="pickerOptions" end-placeholder="结束日期"
                          start-placeholder="开始日期" style="width: 220px" type="daterange" value-format="yyyy-MM-dd"
                          @change="handleChange($event, index)"></el-date-picker>
          <div class="details" @click="handleClick(index)">查看详情<i class="el-icon-arrow-right"></i></div>
        </div>
        <div :ref="item.ref" class="chart"></div>
      </div>
    </div>

    <el-pagination :current-page="mainQuery.current" :page-size="mainQuery.size" :pager-count="5"
                   :total="mainTotal" class="pagination" layout="total,prev,pager,next,jumper,slot"
                   @current-change="mainCurrentChange">
      <el-button class="confirm">确认</el-button>
    </el-pagination>
    <el-dialog :visible.sync="detailsDialog.visible" class="details" title="每日成本" width="1000px">
      <el-table :border="true" :data="data" :stripe="true" height="500px">
        <el-table-column align="center" fixed label="日期" min-width="120" prop="date"/>
        <el-table-column align="center" label="名称" min-width="120" prop="name"/>
        <el-table-column align="center" label="入炉煤含税价" min-width="140" prop="activePrice"/>
        <el-table-column align="center" label="入炉煤不含税价" min-width="140" prop="inPrice"/>
        <el-table-column align="center" label="日涨跌" min-width="140" prop="rise"/>
      </el-table>
      <div class="pagination-container">
        <el-pagination :current-page="query.current" :page-size="query.size" :page-sizes="[50, 100, 200, 500]"
                       :pager-count="5" :total="total" layout="total,pager,next,sizes,jumper,slot" next-text="下一页"
                       style="text-align: right;margin-bottom: 10px;padding-right:10px " @size-change="sizeChange"
                       @current-change="currentChange">
          <el-button class="confirm">确认</el-button>
        </el-pagination>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Model from './model'
import SnProTable from '@/components/Common/SnProTable/index.vue'
import echarts from 'echarts'
import dayjs from 'dayjs'

export default {
  name: 'PlanManage',
  components: {SnProTable},
  data() {
    return {
      pageRef: 'page',
      permissions: {
        save: '*',
        remove: '*'
      },
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      dialogVisible: false,
      detailsDialog: {
        visible: false
      },
      mainQuery: {
        current: 1,
        size: 4
      },
      mainTotal: 0,
      query: {
        current: 1,
        size: 50,
        name: '',
        beginDate: '',
        endDate: ''
      },
      total: '',
      list: [],
      data: []
    }
  },
  computed: {
    actions() {
      return []
    }
  },
  async created() {

  },
  async mounted() {
    this.getData()
  },
  destroyed() {
    window.removeEventListener('resize', this.resize)
  },
  methods: {
    resize() {
      this.list.forEach(item => {
        item.chart.resize()
      })
    },
    initChart(records, index) {
      let colors = [
        {
          axisLine: '#F5F5FF',
          axisLabel: '#9A9A9A',
          series: '#595EC9'
        },
        {
          axisLine: '#F1FBFF',
          axisLabel: '#9A9A9A',
          series: '#FFAF59'
        },
        {
          axisLine: '#FFF9F3',
          axisLabel: '#9A9A9A',
          series: '#FF9D7F'
        },
        {
          axisLine: '#F1FBFF',
          axisLabel: '#9A9A9A',
          series: '#5DC8F4'
        }
      ]
      this.list[index].chart.setOption({
        xAxis: {
          type: 'category',
          data: records.map(item => item.date.substring(0, 10)).reverse(),
          axisTick: {
            show: false
          },
          axisLine: {
            show: true,
            width: 5,
            symbol: ['none', 'arrow'],
            symbolOffset: 30,
            lineStyle: {
              color: colors[index].axisLine,// 更改坐标轴颜色
              type: 'solid',
              shadowOffsetX: 30,
              shadowColor: colors[index].axisLine
            }
          },
          axisLabel: {
            color: colors[index].axisLabel
          }
        },
        yAxis: {
          type: 'value',
          axisTick: {
            show: false
          },
          scale: true,
          splitLine: {
            lineStyle: {
              color: colors[index].axisLine
            }
          },
          axisLine: {
            show: true,
            width: 5,
            symbol: ['none', 'arrow'],
            symbolOffset: 30,
            lineStyle: {
              color: colors[index].axisLine, // 更改坐标轴颜色
              type: 'solid',
              shadowOffsetY: -30,
              shadowColor: colors[index].axisLine
            }
          },
          axisLabel: {
            showMaxLabel: false,
            color: colors[index].axisLabel
          }
        },
        tooltip: {
          trigger: 'axis',
          formatter: (params) => {
            let projectCoal = ''
            if (params[0].data.projectCoal) {
              let list = JSON.parse(params[0].data.projectCoal)
              projectCoal = list.map(item => {
                const _color = item.rise < 0 ? '#33cad9' : item.rise > 0 ? '#FF9D7F' : '#ffffff'
                return `<div>${item.name}：${item.oldPrice}(<span style="color: ${_color}">${item.rise}</span>)</div>`
              }).join('')
            }
            const color = params[0].data.rise < 0 ? '#33cad9' : params[0].data.rise > 0 ? '#FF9D7F' : '#ffffff'
            return `<div>日期：${params[0].axisValue}</div>`
              + `<div>价格：${params[0].data.value}</div>`
              + `<div >日涨跌：<span style="color: ${color};">${params[0].data.rise || 0}</span></div>`
              + `<div style="font-size: 12px">${projectCoal}</div>`
          }
        },
        legend: {
          type: 'plain',
          data: ['价格'],
          bottom: 0,
          selected: {
            '价格': true
          }
        },
        grid: {
          left: '3%',
          right: '40',
          top: '30',
          bottom: '30',
          containLabel: true
        },
        series: [
          {
            data: records.map(item => ({...item, value: item.activePrice})).reverse(),
            type: 'line',
            name: '价格',
            label: {
              show: true,
              color: colors[index].series
            },
            formatter: function (item) {
              return item.data.activePrice
            },
            lineStyle: {
              color: colors[index].series
            },
            itemStyle: {
              color: colors[index].series
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: colors[index].series
                },
                {
                  offset: 1,
                  color: 'white'
                }
              ])
            }
          }
        ]
      })
    },
    async handleChange(e, index) {
      this.query.name = this.list[index].name
      this.query.beginDate = this.list[index].daterange[0]
      this.query.endDate = this.list[index].daterange[1]
      const res = await Model.pageByCoalCategory(this.query)
      this.initChart(res.data.records, index)
    },
    async handleClick(index) {
      this.query.name = this.list[index].name
      this.query.beginDate = this.list[index].daterange[0]
      this.query.endDate = this.list[index].daterange[1]
      const res = await Model.pageByCoalCategory(this.query)
      if (res) {
        this.detailsDialog.visible = true
        this.total = res.data.total
        this.data = res.data.records
      }
    },
    async sizeChange(e) {
      this.query.size = e
      const res = await Model.pageByCoalCategory(this.query)
      if (res) {
        this.detailsDialog.visible = true
        this.total = res.data.total
        this.data = res.data.records
      }
    },
    async currentChange(e) {
      this.query.current = e
      const res = await Model.pageByCoalCategory(this.query)
      if (res) {
        this.detailsDialog.visible = true
        this.total = res.data.total
        this.data = res.data.records
      }
    },
    async mainCurrentChange(e) {
      this.mainQuery.current = e
      this.getData()
    },
    async getData() {
      this.list.forEach(item => {
        if (item.chart) {
          item.chart.dispose()
        }
      })
      const res = await Model.pageProject(this.mainQuery)
      if (res) {
        this.mainTotal = res.data.total
        this.mainQuery.current = res.data.current
        this.list = res.data.records.map((item, index) => {
          const end = new Date()
          const start = new Date()
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
          return {
            name: item.name,
            daterange: [dayjs(start).format('YYYY-MM-DD'), dayjs(end).format('YYYY-MM-DD')],
            ref: `chart${index}`
          }
        })
        await this.$nextTick()
        this.list.forEach(item => {
          item.chart = echarts.init(this.$refs[item.ref][0])
        })
        window.addEventListener('resize', this.resize)
        let promise = this.list.map((item, index) => {
          return Model.pageByCoalCategory({
            name: item.name,
            beginDate: item.daterange[0],
            endDate: item.daterange[1]
          })
        })
        const resList = await Promise.all(promise)
        resList.forEach((item, index) => {
          this.initChart(item.data.records, index)
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-main {
  padding-bottom: 0;
}

.app-container {
  .content {
    padding-bottom: 0;
    display: grid;
    width: 100%;
    height: calc(100vh - 180px);
    padding-top: 10px;
    background-color: #f0f2f5;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr;
    gap: 10px;

    ::v-deep .el-dialog__body {
      padding: 20px;
    }

    ::v-deep .details {

      .el-table th.el-table__cell {
        //background-color: #4169E1;
        background-color: #24c1ab;
        color: #e9e9e9;
      }

      .el-table .cell {
        height: 25px;
        line-height: 25px;
      }

      .el-table__row {
        height: 25px;
      }

      .el-table th.el-table__cell > .cell {
        color: white;
      }

      .el-table tr .el-table__cell {
        padding: 0;
        height: 25px;
        line-height: 25px;
        color: #292929;
      }
    }
  }

  .pagination {
    height: 50px;
    margin-top: 10px;
    width: 100%;
    line-height: 30px;
    padding: 10px;
    text-align: right;
    background-color: #fff;
  }

  .block {
    background-color: #fff;
    overflow: hidden;
    padding: 10px;

    .header {
      display: flex;
      align-items: center;

      .title {
        font-size: 16px;
        color: #0F0F0F;
        margin-right: auto;
        line-height: 35px;
        white-space: nowrap;
      }

      .details {
        cursor: pointer;
        margin-left: 10px;
        font-size: 14px;
        white-space: nowrap;
        color: #6E6E6E;
        line-height: 35px;
      }
    }

    .chart {
      padding-top: 10px;
      width: 100%;
      height: calc(100% - 30px);
    }
  }


}

.pagination-container {
  display: flex;
  margin: 0;
  padding: 15px 0px 0px;
  justify-content: flex-end;
  border: none;

  .el-pager li.active + li {
    border: 1px solid #f1f1f2;
  }

  .el-pager li {
    width: 40px;
    height: 35px;
    line-height: 35px;
    border-radius: 5px;
    border: 1px solid #f1f1f2;
    margin: 0 5px;
  }

  .el-pager li.active {
    background: #2f79e8;
    color: #fff;
  }

  .el-pagination .btn-next,
  .el-pagination .btn-prev {
    margin: 0px;
    border-radius: 5px;
    border: 1px solid #f1f1f2;
    height: 35px;
    line-height: 35px;

    span {
      line-height: 35px;
    }
  }

  .el-pagination button,
  .el-pagination span:not([class*='suffix']) {
    padding: 0 5px;
    // width: 16%;
    border-radius: 5px;
    height: 35px;
    line-height: 35px;
  }

  .el-pagination__editor.el-input .el-input__inner {
    height: 35px;
  }

  .el-pagination .el-select .el-input .el-input__inner {
    height: 35px;
  }

  .confirm {
    padding: 0;
    margin-left: 10px;
    height: 30px !important;
    line-height: 30px !important;;
    background: #2f79e8;
    color: #fff;
    border-radius: 5px;
  }
}
</style>
