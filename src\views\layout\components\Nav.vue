<template>
  <div class="navbar">
    <div class="company-des"><img src="@/assets/navbar/drs.png"></div>
    <div class="company-info">
      <!-- <el-popconfirm confirm-button-text='前往' cancel-button-text='再想想' icon=" el-icon-info" icon-color="blue" title="是否前往实验室?"
                     @confirm="handleToLims">
        <div class="company-lims" slot="reference">
          <img src="@/assets/navbar/limsicon.png">
          <span>Lims</span>
        </div>
      </el-popconfirm> -->

      <div class="company-user">
        <div v-if="isshow">
          <div style=" position: relative;width:50px;" @click="getCount">
            <i class="el-icon-bell" style="font-size:18px"></i>
            <div style=" position: absolute;z-index: 9; top:-4px; right:17px">
              <span class="conttext">{{totalCount}}</span>
            </div>
          </div>
        </div>

        <el-dropdown trigger="click" @command="handleCommands">
          <img src="@/assets/navbar/user_pic.png">
          <span class="el-dropdown-link" style="font-size:16px">
            {{user.loginName}}
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="a">修改密码</el-dropdown-item>
            <el-dropdown-item command="b">退出登录</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </div>

    <el-dialog :append-to-body="true" class="sp-modal" :before-close="handleClose" :visible.sync="dialogExportDownVisible"
               :close-on-press-escape="false" :close-on-click-modal="false" title="修改密码" :width="'500px'" key="downTraitDialog">
      <el-form :model="editForm" ref="editForm" label-width="100px">
        <el-form-item label="旧密码" placeholder="请输入密码" prop="oldPassword" :rules="[{ required: true, message: '密码不能为空'}]">
          <el-input type="password" show-password v-model="editForm.oldPassword">
          </el-input>
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword" :rules="[{ required: true, message: '密码不能为空'}]">
          <el-input type="password" placeholder="请输入新密码" show-password v-model="editForm.newPassword">
          </el-input>
        </el-form-item>
        <div style="display: flex;justify-content: flex-end">
          <el-button @click="handleClose">关闭</el-button>
          <el-button type="primary" @click="handleSubmit">提交</el-button>
        </div>
      </el-form>
    </el-dialog>

    <el-drawer title="系统更新" :visible.sync="drawer" :with-header="false">
      <el-timeline>
        <el-timeline-item v-for="item in sysLogList" :key="item.id" :timestamp="item.createDate" placement="top">
          <el-card>
            <h4>{{item.title}}</h4>
            <p v-for="(text,index) in item.messageList" :key="index">{{text.message}}</p>
          </el-card>
        </el-timeline-item>
      </el-timeline>
    </el-drawer>
  </div>
</template>

<script>
import Func from '@/utils/func'

import { modifyPassword, gethomePushMessage } from '@/api/user'
import { mapGetters } from 'vuex'
import Breadcrumb from '@/components/Breadcrumb'
import Screenfull from '@/components/Screenfull'
import SizeSelect from '@/components/SizeSelect'
import LangSelect from '@/components/LangSelect'
import ThemePicker from '@/components/ThemePicker'
import avatar from '@/assets/avatar.png'
import screenfull from 'screenfull'
import routeObj from '@/lang/zh'
import Model from '@/model/export'
import { findNewsPage } from '@/api/public'

//    Hamburger,TagsView
export default {
  components: {
    Breadcrumb,
    Screenfull,
    SizeSelect,
    LangSelect,
    ThemePicker
  },
  data() {
    return {
      model: Model,
      avatar: avatar,
      menuList: [],
      isRefreshLoading: false,
      dropdownOpen: false,
      info: {},
      editForm: {
        oldPassword: '',
        newPassword: ''
      },
      sysLogList: [],
      drawer: false,
      dialogExportDownVisible: false,
      totalCount: '',
      isshow: true
    }
  },
  computed: {
    ...mapGetters(['sidebar', 'user', 'device', 'currentView'])
  },
  watch: {
    $route: {
      handler({ name, query } = {}) {
        const matched = this.$route.matched
        this.menuList = []
        matched.forEach((v) => {
          const item = {
            path: v.path,
            name: v.name,
            title: routeObj.route[v.name]
          }
          this.menuList.push(item)
        })
      },
      immediate: true
    }
  },
  mounted() {
    this.getnumber()
    this.showRoutes = JSON.parse(localStorage.getItem('showRoutes'))
    let option = { parent: `messagereminder`, Pagename: `message`, params: {} }
    const { parent, child, Pagename, params } = option
    const route = this.showRoutes.find((route) => route.name === parent)
    if (route == undefined) {
      this.isshow = false
    }
  },
  methods: {
    async getnumber() {
      const res = await gethomePushMessage()
      this.totalCount = res.data.totalCount
    },

    getCount() {
      let option = { parent: `messagereminder`, Pagename: `message`, params: {} }
      const { parent, child, Pagename, params } = option
      const route = this.showRoutes.find((route) => route.name === parent)
      // console.log(route)
      // if (route == undefined) {
      //   console.log('没有权限，无法跳转')
      //   this.$message({
      //     type: 'warning',
      //     message: '没有权限，无法跳转',
      //   })
      // } else {
      this.$store.dispatch('changeRoute', route)
      this.$store.dispatch('changeChildRoute', { parent: child, child: '' })
      this.$router.replace({ name: Pagename, params })
      // }
    },
    handleToLims() {
      window.open('http://lims.snxx365.com', 'target')
    },
    handleCommands(v) {
      window.name = ''
      if (v === 'a') {
        this.dialogExportDownVisible = true
      } else if (v === 'b') {
        // let data = {
        //   visible: false,
        // }
        // console.log('0000000000')
        // this.$executeResult(data)
        // setTimeout(() => {

        this.$store.dispatch('LogOut')
        this.$router.replace({ path: `/login` })
        // localStorage.setItem('isclear', true)
        // console.log(localStorage.getItem('isclear'))
        // }, 3000)
      }
    },
    async handleSubmit() {
      const valid = await this.$refs.editForm.validate()
      if (valid) {
        const res = await Func.fetch(modifyPassword, this.editForm)
        if (res.data) {
          this.$message({
            type: 'success',
            message: '已修改密码'
          })
          this.handleClose()
        }
      }
    },
    async bellClick() {
      this.drawer = true
      let res = await Func.fetch(findNewsPage, {
        size: 999,
        orderBy: 'createDate',
        orderDir: 'desc'
      })
      if (res.data) {
        this.sysLogList = res.data.records
      }
    },
    handleClose() {
      this.editForm = { oldPassword: '', newPassword: '' }
      this.dialogExportDownVisible = false
    },
    backward() {
      this.$refs['tags'].$emit('backward')
    },
    forward() {
      this.$refs['tags'].$emit('forward')
    },
    toggleSideBar() {
      this.$store.dispatch('toggleSideBar')
    },
    toogleScreenFull() {
      if (!screenfull.enabled) {
        this.$message({
          message: '浏览器不支持',
          type: 'warning'
        })
        return false
      }
      screenfull.toggle()
    },
    handleCommand(command) {
      switch (command) {
        case 'logout':
          this.logout()
          break
        case 'changePwd':
          this.$router.push('/profile/password')
          break
      }
    },
    logout() {
      localStorage.clear()
      location.reload()
    },
    isActive(route) {
      return route.path === this.$route.path
    },
    refresh() {
      this.$store.dispatch('delCachedView', this.currentView).then(() => {
        const { fullPath } = this.currentView
        this.$nextTick(() => {
          this.$router.replace({
            path: '/redirect' + fullPath
          })
        })
      })
    },
    closeCurrentView() {
      this.$store.dispatch('delView', this.currentView).then(({ visitedViews }) => {
        if (this.isActive(this.currentView)) {
          const latestView = visitedViews.slice(-1)[0]
          if (latestView) {
            this.$router.push(latestView)
          } else {
            this.$router.push('/')
          }
        }
      })
    },
    closeOthersTags() {
      // this.$router.push(this.currentView)
      this.$store.dispatch('delOthersViews', this.currentView)
    },
    closeAllTags() {
      this.$store.dispatch('delAllViews')
      this.$router.push('/')
    },
    back() {
      this.$router.back()
    },
    async initCompanyInfo(status) {
      this.dropdownOpen = status
      if (status && JSON.stringify(this.info) === '{}') {
        // await this.refreshInfo()
      }
    },
    /**
     * 显示下载中心
     * */
    showDownloadCenterHandler() {
      this.dialogExportDownVisible = true
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.el-dropdown-menu--mini .el-dropdown-menu__item {
  line-height: 24px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  font-size: 12px;
  width: 140px;
  &:hover {
    background: #eff0ff;
    color: #2f79e8;
  }
}
$tabHeight: 34px;
.navbar {
  height: 50px;
  background: #fff;
  overflow: hidden;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0px 2px 20px 0px rgba(59, 63, 144, 0.03);
  margin-bottom: 12px;

  .company-des {
    margin: 0 30px;
  }

  .company-info {
    height: inherit;
    margin-right: 10px;
    // width: 280px;
    width: 140px;
    display: flex;
    align-items: center;
    justify-content: space-around;

    .company-lims {
      cursor: pointer;
      position: relative;
      span {
        font-size: 16px;
        color: #000;
      }
    }

    .company-user {
      position: relative;
      img {
        border-radius: 50%;
      }
      .dropdown {
        position: absolute;
      }

      .conttext {
        color: #fff;
        background-color: #ff726b;
        padding: 0 4px;
        border-radius: 5px;
        font-size: 12px;
      }
    }

    .company-lims:first-of-type::after {
      position: absolute;
      height: 15px;
      content: '';
      width: 1px;
      right: -35px;
      top: 5px;
      background: #e5e5e5;
    }

    div {
      display: flex;
      align-items: center;

      span {
        font-size: 13px;
        line-height: 13px;
        margin-left: 10px;
      }
    }
  }

  .hamburger-container {
    line-height: 40px;
    height: 40px;
    padding: 0 10px;
    border-right: 1px solid #e6e6e6;
    position: absolute;
    left: 0;
    background: #fafafa;
    z-index: 2;
  }

  .breadcrumb-container {
    float: left;
  }

  .right-menu {
    float: right;
    height: 100%;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      margin: 0 8px;
    }

    .screenfull {
      height: 20px;
    }

    .international {
      vertical-align: top;
    }

    .theme-switch {
      vertical-align: 15px;
    }

    .avatar-container {
      height: 50px;
      margin-right: 30px;

      .avatar-wrapper {
        cursor: pointer;
        margin-top: 5px;
        position: relative;

        .user-avatar {
          width: 40px;
          height: 40px;
          border-radius: 10px;
        }

        .el-icon-caret-bottom {
          position: absolute;
          right: -20px;
          top: 25px;
          font-size: 12px;
        }
      }
    }
  }

  .nav-btn {
    position: absolute;
    /*width: 40px;*/
    height: 40px;
    text-align: center;
    color: #999;
    z-index: 2;
    top: 0;
    border: none;
    outline: 0;
    font-size: 12px;

    &:hover {
      background: #f2f2f2;
      color: #777;
    }
  }

  .nav-btn-right {
    right: 0;
    border-left: solid 1px #eee;
  }

  .nav-backward {
    right: 300px;
  }

  .nav-forward {
    right: 260px;
  }

  .nav-close {
    width: 60px;
    right: 200px;

    .el-dropdown-link {
      font-size: 16px;
      color: #000;
    }
  }

  .nav-back {
    right: 160px;
  }

  .nav-refresh {
    right: 120px;
  }

  .nav-full-screen {
    right: 80px;
  }

  .nav-bell {
    /*right: 40px;*/
  }

  .nav-setting {
    right: 0;
  }

  .badge {
    display: inline-block;
    min-width: 10px;
    font-size: 12px;
    font-weight: 700;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    border-radius: 10px;
    padding: 3px;
    position: absolute;
    z-index: 100;
    background-color: #ed5565;
    color: #fff;
    top: 2px;
    right: 2px;
  }
}

.companyInfo {
  width: 180px;
  max-height: 600px !important;
}

.breadcrumb-div {
  padding-left: 50px;
  font-size: 14px;
}

.active {
  font-weight: 600;
  color: #000000;
}

.company-view {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
}

.company {
  max-width: 100px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  font-size: 14px;
  color: #000000;

  &:hover {
    /*background: #f2f2f2;*/
    color: #1e6abc !important;
  }
}

.nav-btn2 {
  position: absolute;
  /*width: 40px;*/
  height: 40px;
  text-align: center;
  z-index: 2;
  top: 0;
  border: none;
  outline: 0;
  font-size: 12px;
  background-color: #fafafa;
  display: flex;
  align-items: center;
  border: none !important;
  padding-right: 20px;
}

.select-menu {
  min-width: 120px;

  .menu-item {
    font-size: 14px;
    padding: 10px 20px;
    /*border-bottom: 1px solid #eeeeee;*/
  }

  &:last-child {
    border: none !important;
  }
}
</style>
