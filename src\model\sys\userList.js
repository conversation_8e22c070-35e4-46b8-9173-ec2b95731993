import Model from "@/s-curd/src/model";
import fetch from "@/utils/request";

class UserModel extends Model {
  constructor(data) {
    const dataJson = {
      id: "",
      code: "",
      siteCode: "",
      siteName: "",
      loginName: "",
      password: "",
      jianpin: "",
      pinyin: "",
      name: "",
      email: "",
      phone: "",
      mobile: "",
      address: "",
      state: "ZC",
      lastLoginIp: "",
      lastLoginDate: "",
      avatar: "",
      gender: "",
      birthday: "",
      height: "",
      weight: "",
      hiredate: "",
      dimissionDate: "",
      registerTime: "",
      nativePlace: "",
      homeContactName: "",
      homeContactPhone: "",
      createBy: "",
      createDate: "",
      updateBy: "",
      updateDate: "",
      ext: "",
      remarks: "",
      no: "",
      roleCodes: "",
      factoryNameList: "",
      factoryName: "",
    };
    const form = {};
    const tableOption = {
      showSetting: true,
      add: false,
      edit: false,
      del: false,
      showIndex: true,
      showPage: true,
      showSelection: true,
      mountQuery: true,

      columns: [
        {
          label: "综合查询",
          width: 150,
          align: "center",
          isShow: false,
          filter: {
            label: "综合查询",
            prop: "filter_LIKES_no_OR_name_OR_loginName_OR_phone_OR_mobile",
          },
          isFilter: true,
        },
        {
          label: "姓名",
          prop: "name",
          align: "center",
          slot: "name",
          isFilter: true,
          isShow: true,
          filter: {
            label: "姓名",
            prop: "filter_LIKES_name",
          },
        },
        {
          label: "登录名",
          prop: "loginName",
          isFilter: true,
          isShow: true,
          filter: {
            label: "登录名",
            prop: "filter_LIKES_loginName",
          },
        },
        {
          label: "供应组",
          prop: "groupName",
          isShow: true,
        },
        {
          label: "部门",
          prop: "deptName",
          isShow: true,
        },
        {
          label: "工厂",
          prop: "factoryName",
          isShow: true,
        },

        {
          label: "角色",
          prop: "roleNames",
          isShow: true,
        },

        {
          label: "工号",
          prop: "no",
          isShow: true,
        },
        {
          label: "住址",
          prop: "address",
          isFilter: true,
          isShow: true,
          filter: {
            label: "住址",
            prop: "filter_LIKES_address",
          },
        },
        {
          label: "电话",
          prop: "phone",
          isFilter: true,
          isShow: true,
          filter: {
            label: "电话",
            prop: "filter_LIKES_phone",
          },
        },
        {
          label: "手机",
          prop: "mobile",
          isFilter: true,
          isShow: true,
          filter: {
            label: "手机",
            prop: "filter_LIKES_mobile",
          },
        },

        {
          label: "邮箱",
          prop: "email",
          isFilter: true,
          isShow: true,
          filter: {
            label: "邮箱",
            prop: "filter_LIKES_email",
          },
        },
        {
          label: "起始日期",
          prop: "createDate",
          isFilter: true,
          filter: {
            label: "起始日期",
            start: "filter_GED_createDate",
            end: "filter_LED_createDate",
          },
          component: "DateRanger",
          isShow: true,
          width: 150,
        },
        {
          label: "截止日期",
          prop: "expireDate",
          isFilter: true,
          filter: {
            label: "截止日期",
            start: "filter_GED_expireDate",
            end: "filter_LED_expireDate",
          },
          component: "DateRanger",
          isShow: true,
          width: 150,
        },
        {
          label: "入职时间",
          prop: "hiredate",
          isFilter: true,
          filter: {
            label: "入职时间",
            start: "filter_GED_hiredate",
            end: "filter_LED_hiredate",
          },
          component: "DateRanger",
          isShow: true,
          width: 150,
        },
        {
          label: "离职时间",
          prop: "dimissionDate",
          isShow: true,
          isFilter: true,
          filter: {
            label: "离职时间",
            start: "filter_GED_dimissionDate",
            end: "filter_LED_dimissionDate",
          },
          component: "DateRanger",
          width: 150,
        },
        {
          label: "操作",
          prop: "opt",
          isShow: true,
          slot: "opt",
          width: 150,
        },
      ],
    };
    super("/cwe/a/user", dataJson, form, tableOption);
  }

  page(searchForm) {
    return fetch({
      url: "/cwe/a/user/getPage",
      method: "get",
      params: { ...searchForm },
    });
  }

  save(data) {
    return fetch({
      url: this.name + "/saveUser",
      method: "post",
      data: data,
    });
  }
}

export default new UserModel();
