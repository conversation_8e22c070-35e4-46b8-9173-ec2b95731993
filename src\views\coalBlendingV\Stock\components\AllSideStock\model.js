// eslint-disable-next-line

import BaseModel, {TableConfig, FormConfig} from '@/components/Common/SnProTable/model'
import CalcUtils from '@/utils/calcUtils'

import {dateUtil, formatToDateTime} from '@/utils/dateUtils'
import {isEmpty} from "@/utils/is";
import {fetchSetting, MERGE_TYPE} from "@/const";

export const name = `/cwe/a/coal`

const createFormConfig = (options = {}) => {
    return {
        labelHideAll: false,
        fieldMapToTime: [['_dateRange', ['beginDate', 'endDate'], 'YYYY-MM-DD']],
        fieldMapToNumber: [
            ['ad', ['minAd', 'maxAd']],
            ['std', ['minStd', 'maxStd']],
            ['vdaf', ['minVdaf', 'maxVdaf']],
            ['g', ['minG', 'maxG']],
            ['y', ['minY', 'maxY']],
            ['csr', ['minCsr', 'maxCsr']],
        ],
        filters: [
            {
                label: '日期',
                hideLabel: true,
                prop: '_dateRange',
                component: 'SnDateRanger',
                defaultValue: [],
                componentProp: {
                    valueFormat: 'yyyy-MM-dd'
                },
                style: {
                    width: '220px'
                }
            },
            {
                label: '品名',
                hideLabel: true,
                prop: 'coalName',
                itemStyle: {
                    width: '110px',
                },
                componentProp: {}
            },
            {
                label: '煤种名称',
                hideLabel: true,
                prop: 'coalCategoryName',
                itemStyle: {
                    width: '110px',
                },
                componentProp: {}
            },
            {
                label: '产地',
                hideLabel: true,
                itemStyle: {
                    width: '100px',
                },
                componentProp: {},
                prop: 'location',
            },
            {
                label: 'Ad',
                prop: 'ad',
                hideLabel: true,
                component: 'SnSimpleInputRanger',
                defaultValue: [],
                style: {
                    width: '140px',
                },
                componentProp: {
                    useProFormItem: false,
                    startPlaceholder: '',
                    endPlaceholder: '',
                    rangeSeparator: '≤Ad≤ ',
                }
            },
            {
                label: 'Std',
                prop: 'std',
                itemStyle: {
                    width: '140px',
                },
                hideLabel: true,
                component: 'SnSimpleInputRanger',
                defaultValue: [],
                style: {
                    width: '140px',
                },
                componentProp: {
                    useProFormItem: false,
                    startPlaceholder: '',
                    endPlaceholder: '',
                    rangeSeparator: '≤Std≤ ',
                }
            },
            {
                label: 'Vdaf',
                prop: 'vdaf',
                hideLabel: true,
                component: 'SnSimpleInputRanger',
                defaultValue: [],
                style: {
                    width: '140px',
                },
                componentProp: {
                    useProFormItem: false,
                    startPlaceholder: '',
                    endPlaceholder: '',
                    rangeSeparator: '≤Vdaf≤ ',
                }
            },
            {
                label: 'G',
                prop: 'g',
                hideLabel: true,
                component: 'SnSimpleInputRanger',
                defaultValue: [],
                style: {
                    width: '140px',
                },
                componentProp: {
                    useProFormItem: false,
                    startPlaceholder: '',
                    endPlaceholder: '',
                    rangeSeparator: '≤G≤ ',
                }
            },
            {
                label: 'Y',
                prop: 'y',
                hideLabel: true,
                component: 'SnSimpleInputRanger',
                defaultValue: [],
                style: {
                    width: '140px',
                },
                componentProp: {
                    useProFormItem: false,
                    startPlaceholder: '',
                    endPlaceholder: '',
                    rangeSeparator: '≤Y≤ ',
                }
            },
            {
                label: 'CSR',
                prop: 'csr',
                hideLabel: true,
                component: 'SnSimpleInputRanger',
                defaultValue: [],
                style: {
                    width: '150px',
                },
                componentProp: {
                    useProFormItem: false,
                    startPlaceholder: '',
                    endPlaceholder: '',
                    rangeSeparator: '≤CSR≤ ',
                }
            },
            {
                label: 'Fe2O3≤ ',
                itemStyle: {
                    width: '140px',
                },
                componentProp: {
                    placeholder: ' ',
                },
                prop: 'maxFe2O',
            },
            {
                label: 'CaO≤ ',
                itemStyle: {
                    width: '130px',
                },
                componentProp: {
                    placeholder: ' ',
                },
                prop: 'maxCaO',
            },
            {
                label: 'MF≥ ',
                itemStyle: {
                    width: '130px',
                },
                componentProp: {
                    placeholder: ' ',
                },
                prop: 'minProcMf',
            },
        ],
        ...options.formConfig
    }
}

/**
 * 表格配置
 * @returns {TableConfig}
 */
const createTableConfig = (options = {}) => {
    return {
        mountedQuery: true,
        showOpt: false, // 显示操作列
        showIndex: false, // 显示序号列
        showSelection: false, // 显示选择列
        useApiList: false,
        optConfig: {width: 100, label: '操作', prop: 'opt', fixed: 'right'},
        columns: [
            {
                prop: 'date',
                label: '更新日期',
                fixed: true,
                width: 100,
                slot: 'date',
                align: 'center',
            },

            {
                label: '煤种名称',
                prop: 'coalCategoryName',
                fixed: true,
                slot: 'coalCategoryName',
                isEdit: true,
                width: 100
            },
            {
                label: '品名',
                fixed: true,
                prop: 'coalName',
                slot: 'coalName',
                isEdit: true,
                width: 100
            },


            {
                label: '来源',
                prop: 'coalSource',
                width: 60,
                // slot: 'coalSource',
                // isEdit: true,
                format: 'dict|b_coal_source_type'
            },

            {
                prop: 'stock',
                label: '库存',
                align: 'center',
                slot: 'stock',
                width: 70,
                isEdit: true,
            },
            {
                prop: 'mt',
                label: '水分Mt%',
                align: 'center',
                width: 70,
                slot: 'mt',
                isEdit: true,
            },
            {
                prop: 'ad',
                label: '灰分Ad%',
                align: 'center',
                slot: 'ad',
                width: 70,
                isEdit: true,

            },
            {
                prop: 'std',
                label: '硫分St,d%',
                align: 'center',
                slot: 'std',
                width: 70,
                isEdit: true,

            },
            {
                prop: 'vdaf',
                label: '挥发分Vdaf%',
                width: 90,
                align: 'center',
                slot: 'vdaf',
                isEdit: true,

            },
            {
                prop: 'g',
                label: '粘结G',
                align: 'center',
                width: 70,
                slot: 'g',
                isEdit: true,
            },
            {
                prop: 'y',
                align: 'center',
                width: 70,
                label: 'Y/mm',
                slot: 'y',
                isEdit: true,
            },

            {
                prop: 'x',
                align: 'center',
                width: 70,
                label: 'X/mm',
                slot: 'x',
                isEdit: true,
            },


            {
                "label": "反射率",
                width: 70,
                "prop": "macR0",
                slot: 'macR0',
                decimalPlaces: 3,
                isEdit: true,
            },
            {
                "label": "标准差",
                width: 70,
                "prop": "macS",
                slot: 'macS',
                isEdit: true,
                decimalPlaces: 3,

            },
            {
                "label": "CRI%",
                width: 70,
                "prop": "cri",
                slot: 'cri',

                isEdit: true,

            },
            {
                "label": "CSR%",
                width: 70,
                "prop": "csr",
                slot: 'csr',
                isEdit: true,
            },

            {
                "label": "不含税煤价",
                width: 90,
                "prop": "activePriceCoalPriceNoTax",
                slot: 'activePriceCoalPriceNoTax',
                isEdit: true,
            },
            {
                "label": "含税煤价",
                width: 90,
                "prop": "activePriceCoalPriceWithTax",
                slot: 'activePriceCoalPriceWithTax',
                isEdit: true,
            },
            {
                "label": "不含税运费",
                width: 90,
                "prop": "activePriceTransportPriceNoTax",
                slot: 'activePriceTransportPriceNoTax',
                isEdit: true,
            },
            {
                "label": "路耗%",
                "prop": "activePriceTransportCostP1",
                slot: 'activePriceTransportCostP1',
                isEdit: true,
            },
            {
                "label": "分类",
                "prop": "categoryType",
                slot: 'categoryType',
                isEdit: true,
            },

            {
                "label": "不含税到厂煤价",
                width: 100,
                isEdit: true,
                slot: 'activePriceFactoryPriceNoTax',
                "prop": "activePriceFactoryPriceNoTax"
            },
            {
                "label": "折水标准",
                "prop": "reduceMtStandard",
                slot: 'reduceMtStandard',
                isEdit: true,
            },
            {
                "label": "折水不含税进厂价",
                width: 120,
                isEdit: true,
                slot: 'reduceMtFactoryPriceNoTax',
                "prop": "reduceMtFactoryPriceNoTax"
            },
            {
                "label": "配煤成本",
                "prop": "coalBlendingCost",
                slot: 'coalBlendingCost',
                isEdit: true,
            },
            {
                "label": "产地",
                width: 140,
                "prop": "location",
                slot: 'location',
                isEdit: true,

            },
            {
                "label": "备注",
                "prop": "remarks",
                showOverflowTooltip: true,
                width: 100,
                slot: 'remarks',
                isEdit: true,
            },

            {
                "label": "MF/ddpm",
                "prop": "procMf"
            },
            {
                "label": "奥亚膨胀b%",
                "prop": "procB",

            },
            {
                "label": "奥亚收缩a%",
                "prop": "procA",

            },


            {
                "label": "Vd%",
                "prop": "vd",

            },

            {
                "label": "SiO2%",
                "prop": "siO2",

            },

            {
                "label": "Al2O3%",
                "prop": "al2O3",

            },
            {
                "label": "Fe2O3%",
                "prop": "fe2O3",

            },

            {
                "label": "CaO%",
                "prop": "caO",

            },

            {
                "label": "K2O%",
                "prop": "k2O",

            },

            {
                "label": "Na2O%",
                "prop": "na2O",

            },

            {
                "label": "MgO%",
                "prop": "mgO",


            },

            {
                "label": "TiO2%",
                "prop": "tiO2",

            },

            {
                "label": "MnO2%",
                "prop": "mnO2",

            },

            {
                "label": "P205%",
                "prop": "p2O5",

            },

            {
                "label": "SO3%",
                "prop": "so3",

            },

            {
                "label": "MCI%",
                "prop": "mci",

            },

            {
                "label": "活惰比",
                "prop": "activeInertiaRatio"
            },
            {
                label: '分析煤种',
                prop: 'analysisCategoryName',
                width: 80
            },
        ],
        ...options.tableConfig
    }
}

export const getEditCols = () => {
    return createTableConfig().columns.filter(v => v.isEdit)
}

export class Model extends BaseModel {
    constructor(options = {}) {
        super(name, createFormConfig(options), createTableConfig(options))
    }

    async page(query) {
        const resp = await super.request({
            url: `/cwe/a/coalDatabase/data/page`,
            method: 'get',
            params: query
        })

        const getRangeValues = (v) => {
            try {
                const proportionValues = v.proportionContent ? JSON.parse(v.proportionContent) : []
                const rangeNameValues = v.rangeContent ? JSON.parse(v.rangeContent) : []
                const rangeValues = proportionValues.map((_, index) => ({
                    rangeName: rangeNameValues[index],
                    proportion: proportionValues[index],
                }))
                return rangeValues
            } catch (e) {
                console.log(e)
                return []
            }
        }

        resp.data[fetchSetting.listField].forEach(v => {
            v.rangeValues = getRangeValues(v)
        })
        return resp
    }

    add(data) {
        Model.formatSubmitData(data)
        return super.request({
            url: `/cwe/a/coalDatabase/data/add`,
            method: 'post',
            data: {
                ...data,
                ContentType: 'application/json'
            }
        })
    }


    static formatSubmitData(data) {
        data.data.forEach(v => {
            // 不含税煤价为空，含税煤价有值时
            if (!v.activePriceCoalPriceNoTax && v.activePriceCoalPriceWithTax) {
                v.activePriceCoalPriceNoTax = CalcUtils.getShowValue(CalcUtils.divide(v.activePriceCoalPriceWithTax, 1.13), 2)
            }
            if (!v.activePriceCoalPriceWithTax && v.activePriceCoalPriceNoTax) {
                v.activePriceCoalPriceWithTax = CalcUtils.getShowValue(CalcUtils.multiply(v.activePriceCoalPriceNoTax, 1.13), 2)
            }
            const isEmpty = (item) => ['', undefined, null].includes(item)
            if (!isEmpty(v.activePriceFactoryPriceNoTax)) {
                v.activePriceFactoryPriceNoTax = CalcUtils.getShowValue(v.activePriceFactoryPriceNoTax, 2)
            }
            if (!isEmpty(v.reduceMtFactoryPriceNoTax)) {
                v.reduceMtFactoryPriceNoTax = CalcUtils.getShowValue(v.reduceMtFactoryPriceNoTax, 2)
            }
            if (!isEmpty(v.coalBlendingCost)) {
                v.coalBlendingCost = CalcUtils.getShowValue(v.coalBlendingCost, 2)
            }
        })
    }


    get(query) {
        return super.request({
            url: `/cwe/a/coalDatabase/data/get`,
            method: 'get',
            params: query
        })
    }

    /**
     * 更新
     * @param data
     */
    update(data) {
        Model.formatSubmitData(data)
        return super.request({
            url: `/cwe/a/coalDatabase/data/update`,
            method: 'post',
            data: {
                ...data,
                ContentType: 'application/json'
            }
        })
    }


    /**
     * 合并外部
     */
    mergeData(data) {
        return super.request({
            url: `/cwe/a/coalDatabase/data/merge`,
            method: 'post',
            data: {
                ...data,
                ContentType: 'application/json'
            }
        })
    }


    /**
     * 批量改折水、路耗
     */
    batchUpdateMt(data) {
        return super.request({
            url: `/cwe/a/coalDatabase/data/reduce-mt-transport-cost/batch-update`,
            method: 'post',
            data: {
                ...data,
                ContentType: 'application/json'
            }
        })
    }


    sortTop({id, sort, coalSource}) {
        return super.request({
            // sort如果大于0则取消置顶
            url: `/cwe/a/coalDatabase/data//${sort ? 'un-sort-top' : 'sort-top'}`,
            method: 'get',
            params: {id, coalSource}
        })
    }

    async remove({id, coalSource, useConfirm = false, type}) {
        const fetchFn = () =>
            super.request({
                url: type === MERGE_TYPE.MERGE ? `/cwe/a/coalDatabase/data/un-merge` : `/cwe/a/coalDatabase/data/delete`,
                method: 'get',
                params: {id, coalSource}
            })

        if (useConfirm) {
            try {
                const ctx = type === MERGE_TYPE.MERGE ? '是否取消合并?' : '是否确认删除当前数据?'
                const res = await super.modal.confirm(ctx)
                return fetchFn()
            } catch (error) {
                return Promise.reject(error)
            }
        } else {
            return fetchFn()
        }
    }


}

export default Model
