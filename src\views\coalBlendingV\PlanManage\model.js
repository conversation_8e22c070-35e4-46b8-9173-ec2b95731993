// eslint-disable-next-line

import BaseModel, {TableConfig, FormConfig} from '@/components/Common/SnProTable/model'
import {dateUtil, formatToDateTime} from '@/utils/dateUtils'
import {MERGE_TYPE} from '@/const'
import request from '@/utils/request'

export const name = `/cwe/a/coalWashingProject`

const createFormConfig = () => {
  return {
    // fieldMapToTime: [['_dateRange', ['beginDate', 'endDate'], 'YYYY-MM-DD']],
    filters: [
      {
        label: '品名',
        prop: 'filter_LIKES_name'
      },
      {
        hide: true,
        prop: 'orderBy',
        defaultValue: 'createDate'
      },
      {
        hide: true,
        prop: 'orderDir',
        defaultValue: 'desc'
      }
    ]
  }
}

/**
 * 表格配置
 * @returns {TableConfig}
 */
const createTableConfig = () => {
  return {
    mountedQuery: true,
    showOpt: true, // 显示操作列
    showIndex: false, // 显示序号列
    showSelection: false, // 显示选择列
    useApiList: false,
    optConfig: {
      width: 180,
      label: '操作',
      prop: 'opt',
      fixed: 'right'
    },
    columns: [
      {
        slot: 'createDate',
        label: '时间',
        isShow: true,
        prop: 'createDate',
        isFilter: true
        // filter: {
        //     prop: 'filter_LIKES_name',
        //     label: '名称'
        // }
      },
      {
        slot: 'name',
        label: '名称',
        isShow: true,
        prop: 'name',
        isFilter: true,
        filter: {
          prop: 'filter_LIKES_name',
          label: '名称'
        }
      },
      {
        label: 'Ad',
        prop: 'inAd',
        isShow: true,
        minWidth: 60
      },
      {
        label: 'St,d',
        prop: 'inStd',
        isShow: true,
        minWidth: 60
      },
      {
        label: 'G',
        prop: 'inG',
        isShow: true,
        minWidth: 60
      },
      {
        label: '入炉煤不含税价(水8)',
        prop: 'inPrice',
        isShow: true,
        minWidth: 100
      },
      {
        label: '入炉煤含税价',
        prop: 'activePrice',
        isShow: true,
        minWidth: 100
      },
      {
        label: '反射率',
        prop: 'theoryMacR0',
        isShow: true,
        minWidth: 60
      },
      {
        label: '标准差',
        prop: 'theoryMacS',
        isShow: true,
        minWidth: 60
      },

      {
        label: 'CSR',
        prop: 'gyCsr',
        isShow: true,
        minWidth: 60
      },
      {
        label: '焦炭价格',
        isShow: true,
        isFilter: true,
        prop: 'jtPrice',
        minWidth: 100,
        filter: {
          label: '焦炭成本',
          start: 'filter_GEM_jtPrice',
          end: 'filter_LEM_jtPrice',
          component: 'NumberRanger'
        }
      },
      {
        label: '是否跟踪',
        isShow: true,
        prop: 'isTrace',
        slot: 'isTrace',
        minWidth: 80
      }
    ]
  }
}

export class Model extends BaseModel {
  constructor() {
    super(name, createFormConfig(), createTableConfig())
  }

  page(params) {
    return request({
      url: `${name}/pageCoalProject`,
      method: 'get',
      params
    })
  }

  async remove({id, useConfirm = false}) {
    const fetchFn = () =>
      super.request({
        url: `/cwe/a/coalWashingProject/deleteById`,
        method: 'post',
        data: {id}
      })

    if (useConfirm) {
      try {
        const ctx = '是否确认删除当前数据?'
        const res = await super.modal.confirm(ctx)
        return fetchFn()
      } catch (error) {
        return Promise.reject(error)
      }
    } else {
      return fetchFn()
    }
  }

  saveIsTrace(data) {
    return request({
      url: `${name}/saveIsTrace`,
      method: 'post',
      data
    })
  }

  coalWashingExport(params) {
    return request({
      url: `${name}/coalWashing-export`,
      method: 'get',
      params,
      responseType: 'blob'
    })
  }

}

export default new Model()
