import fetch from '@/utils/request'

/**
 * 列表数据
 * @param filter
 */
export function getCoalRock(filter) {
  return fetch({
    url: '/cwe/a/workspaceCoalRock/getMySpace',
    method: 'get',
    params: {
      ...filter
    }
  })
}

export function deleteCoalRockById(filter) {
  return fetch({
    url: '/cwe/a/workspaceCoalRock/delete',
    method: 'post',
    data: filter
  })
}

export function coalRockBlending(filter) {
  return fetch({
    url: '/cwe/a/workspaceCoalRock/coalRockQualityOptimizeByType',
    method: 'post',
    data: filter
  })
}

export function saveProjectApi(filter) {
  return fetch({
    url: '/cwe/a/userWorkspaceCoalRockResult/saveUserWorkspaceCoalRockResult',
    method: 'post',
    data: filter
  })
}

export function saveNewProjectApi(filter) {
  return fetch({
    url: '/cwe/a/coalRockProject/saveCoalRockProject',
    method: 'post',
    data: filter
  })
}

export function saveWashProjectApi(filter) {
  return fetch({
    url: '/cwe/a/coalWashingProject/saveCoalWashingProject',
    method: 'post',
    data: filter
  })
}

export function remakeCoal(filter) {
  return fetch({
    url: '/cwe/a/coalRockProject/coalRockQualityOptimizeRedo',
    method: 'get',
    params: filter
  })
}

export function remakeNewCoal(filter) {
  return fetch({
    url: '/cwe/a/coalWashingProject/coalWashingQualityOptimizeRedo',
    method: 'get',
    params: filter
  })
}

export function getName(filter) {
  return fetch({
    url: '/cwe/a/coalRockProject/getName',
    method: 'get',
    params: filter
  })
}

export function deleteById(filter) {
  return fetch({
    url: '/cwe/a/workspaceCoalRock/delete',
    method: 'post',
    data: filter
  })
}

export function syncCoalRock(filter) {
  return fetch({
    url: '/cwe/a/workspaceCoalRock/syncCoal',
    method: 'post',
    data: filter
  })
}

export function syncStock(filter) {
  return fetch({
    url: '/cwe/a/workspaceCoalWashing/syncStock',
    method: 'post',
    data: filter
  })
}

export function syncCoal(filter) {
  return fetch({
    url: '/cwe/a/workspaceCoalWashing/syncCoal',
    method: 'post',
    data: filter
  })
}

export function removeAllCoal(filter) {
  return fetch({
    url: '/cwe/a/workspaceCoalRock/removeAllCoal',
    method: 'post',
    data: filter
  })
}

export function getRockCoalById(filter) {
  return fetch({
    url: '/cwe/a/workspaceCoalRock/getCoalById',
    method: 'get',
    params: filter
  })
}

export function getNewRockCoalById(filter) {
  return fetch({
    url: '/cwe/a/workspaceCoalWashing/getCoalById',
    method: 'get',
    params: filter
  })
}

export function getWashCoalById(filter) {
  return fetch({
    url: '/cwe/a/coalWashingProject/get',
    method: 'get',
    params: filter
  })
}

export function getNewCoalRock(filter) {
  return fetch({
    url: '/cwe/a/workspaceCoalWashing/getMySpace',
    method: 'get',
    params: filter
  })
}

export function addCoalWashing(filter) {
  return fetch({
    url: '/cwe/a/workspaceCoalWashing/addCoals',
    method: 'post',
    data: filter
  })
}

export function deleteWashingById(filter) {
  return fetch({
    url: '/cwe/a/workspaceCoalWashing/deleteById',
    method: 'post',
    data: filter
  })
}

export function coalWashingQualityOptimizeByType(filter) {
  return fetch({
    url: '/cwe/a/workspaceCoalWashing/coalWashingQualityOptimizeByType',
    method: 'post',
    data: filter
  })
}

export function getWashName(filter) {
  return fetch({
    url: '/cwe/a/coalWashingProject/getName',
    method: 'get',
    params: filter
  })
}

export function removeWashAllCoal(filter) {
  return fetch({
    url: '/cwe/a/workspaceCoalWashing/removeAllCoal',
    method: 'post',
    data: filter
  })
}

//新增配煤
export function workspaceCoalWashingAddCoals(data) {
  return fetch({
    url: '/cwe/a/workspaceCoalWashing/addCoals',
    method: 'post',
    data: {
      ...data,
      ContentType: 'application/json'
    }
  })
}

//保存铲数和吨数数据
export function saveData(rowData) {
  return fetch({
    url: '/cwe/a/workspaceCoalWashingNumber/save',
    method: 'post',
    data: rowData
  })
}

export function getshovelTonnage(filter) {
  return fetch({
    url: '/cwe/a/workspaceCoalWashingNumber/findPageOrderBySetTop',
    method: 'get',
    params: filter
  })
}
//删除接口
export function deleteProjectName(deleteData) {
  return fetch({
    url: '/cwe/a/workspaceCoalWashingNumber/deleteProjectName',
    method: 'post',
    data: deleteData
  })
}