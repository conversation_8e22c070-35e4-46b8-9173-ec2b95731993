<template>
  <div class="app-container">
    <SnProTable :ref="pageRef" :actions="actions" :afterFetch="afterFetch" :beforeFetch="beforeFetch" :model="model"
                @row-click="handleClickRow" @row-class-name="rowClassName">
      <template #date="{ col }">
        <el-table-column v-bind="col">
          <template #default="{row,$index}">
            <div style="position: relative;overflow: hidden;">
              <span>{{ getDate(row[col.prop]) }}</span>
              <!--                            <div v-if="row.manualUpdated ==='Y'" class="tag-left-right">-->
              <!--                                <span class="triangle-text">人工</span>-->
              <!--                            </div>-->
            </div>
          </template>
        </el-table-column>
      </template>

      <template v-for="item in getColSlots" #[item.prop]="{ col }">
        <el-table-column v-bind="col">
          <template #default="{row}">
            <template v-if="!row.isEdit">
              <span v-if="col.decimalPlaces"> {{
                                getShowValue(row[col.prop], col.decimalPlaces, 'ROUND_HALF_UP')
                                }}</span>
              <span v-else> {{ row[col.prop] }}</span>
            </template>

            <template v-else>
              <el-autocomplete v-if="item.prop ==='coalCategoryName'" v-model="row[`${col.prop}${editKey}`]"
                               :fetch-suggestions="querySearch" class="noPaddingInput">
              </el-autocomplete>

              <SnSimpleSelect v-else-if="item.prop ==='categoryType'" v-model="row[`${col.prop}${editKey}`]"
                              class="noPaddingInput" :list="[
                                        {id: '精煤', name: '精煤'},
                                        {id: '原煤', name: '原煤'},
                                    ]" />

              <el-input v-else v-model="row[`${col.prop}${editKey}`]" :placeholder="`${col.label}`" :type="item.type"
                        @input="handleInputChange(row,col)" class="noPaddingInput">
              </el-input>
            </template>
          </template>
        </el-table-column>
      </template>

      <template #coalName="{ col }">
        <el-table-column v-bind="col">
          <template #default="{row}">
            <div style="position: relative;overflow: hidden;">
              <div style="color: rgb(37, 109, 223);cursor: pointer" @click="showCoalDetail(row)">{{ row[col.prop] }}</div>
              <div v-if="row.isCoalRock ==='Y'" style="background:#FF8B41" class="tag-left-right">
                <span class="triangle-text">岩</span>
              </div>
              <div v-if="row.type === 'nb'" class="tag-left-right" style="background:#FF8B41">
                <span class="triangle-text">人工</span>
              </div>
            </div>
          </template>
        </el-table-column>
      </template>

      <template #activePriceFactoryPriceNoTax="{ col }">
        <el-table-column v-bind="col">
          <template #default="{row}">
            <span>{{
                getShowValue(row.isEdit ? row[`${col.prop}${editKey}`] : row[col.prop], 2, 'ROUND_HALF_UP')
                }} </span></template>
        </el-table-column>
      </template>

      <template #factoryPrice="{ col }">
        <el-table-column v-bind="col">
          <template #default="{row}">
            <template v-if="!row.isEdit">
              <span>{{ getShowValue(row[col.prop], 2, 'ROUND_HALF_UP') }}</span>
            </template>
            <template v-else>
              <el-input v-model="row[`${col.prop}${editKey}`]" :placeholder="`${col.label}`" type="number"
                        class="noPaddingInput" @input="handleInputChange(row,col)">
              </el-input>
            </template>
          </template>
        </el-table-column>
      </template>

      <template #vd="{ col }">
        <el-table-column v-bind="col">
          <template #default="{row}">
            <span>{{
                getShowValue(row.isEdit ? row[`${col.prop}${editKey}`] : row[col.prop], 2, 'ROUND_HALF_UP')
                }} </span></template>
        </el-table-column>
      </template>

      <template #mci="{ col }">
        <el-table-column v-bind="col">
          <template #default="{row}">
            <span>{{
                getShowValue(row.isEdit ? row[`${col.prop}${editKey}`] : row[col.prop], 2, 'ROUND_HALF_UP')
                }} </span></template>
        </el-table-column>
      </template>

      <template #reduceMtFactoryPriceNoTax="{ col }">
        <el-table-column v-bind="col">
          <template #default="{row}">
            <span>{{
                getShowValue(row.isEdit ? row[`${col.prop}${editKey}`] : row[col.prop], 2, 'ROUND_HALF_UP')
                }} </span>
          </template>
        </el-table-column>
      </template>

      <!--            <template #coalBlendingCost="{ col }">-->
      <!--                <el-table-column v-bind="col">-->
      <!--                    <template #default="{row}">-->
      <!--            <span>{{-->
      <!--                getShowValue(row.isEdit ? row[`${col.prop}${editKey}`] : row[col.prop], 2, 'ROUND_HALF_UP')-->
      <!--                }} </span>-->
      <!--                    </template>-->
      <!--                </el-table-column>-->
      <!--            </template>-->
      <!--            </template>-->

      <template #procX="{ col }">
        <el-table-column v-bind="col">
          <template #default="{row}">
            <template v-if="!row.isEdit">
              <span> {{ row[col.prop] }}</span>
            </template>
            <template v-else>
              <el-input v-model="row[`${col.prop}${editKey}`]" :placeholder="`${col.label}`" type="number"
                        class="noPaddingInput" @input="handleInputChange(row,col)">
              </el-input>
            </template>
          </template>
        </el-table-column>
      </template>

      <template #opt="{ row,...rest }">
        <template v-if="checkPermission([permissions.save])">
          <el-button v-if="!row.isEdit" type="text" @click.stop="handleRow(row, 'edit', rest)">
            编辑
          </el-button>
          <template v-else>
            <el-button type="text" :loading="row.loading" @click.stop="handleRow(row, 'save', rest)">
              保存
            </el-button>
            <el-divider direction="vertical"></el-divider>
            <el-button type="text" :loading="row.loading" @click.stop="handleRow(row, 'cancel', rest)">
              取消
            </el-button>
          </template>

          <el-divider direction="vertical"></el-divider>
        </template>

        <!-- 外部数据显示删除按钮 -->
        <template v-if="checkPermission([permissions.remove]) && (row.type === 'wb' || row.type === 'zsmy')">
          <el-button v-if="MERGE_TYPE.MERGE === row.mergeType" type="text" @click.stop="handleRemove(row)">
            取消合并
          </el-button>
          <el-button v-else type="text" @click.stop="handleRemove(row)">删除</el-button>
          <el-divider direction="vertical"></el-divider>
        </template>
        
        <!-- 置顶按钮对所有来源都显示 -->
        <el-button v-if="checkPermission([permissions.save])" type="text" @click.stop="handleSortTop(row)">
          {{ row.sort ? '取消置顶' : '置顶' }}
        </el-button>
        
        <!-- 内部数据显示刷新煤质按钮 -->
        <template v-if="row.type === 'nb'">
          <el-divider direction="vertical"></el-divider>
          <el-button type="text" @click.stop="handleRefreshCoalQuality(row)">
            刷新煤质
          </el-button>
        </template>
      </template>

      <!-- 为所有密度区间添加模板插槽 -->
      <!-- <template v-for="range in densityRanges" #[range]="{ col }">
        <el-table-column v-bind="col">
          <template #default="{row}">
            <template v-if="!row.isEdit">
              <span>{{ row[col.prop] !== undefined ? row[col.prop].toFixed(1) : '' }}</span>
            </template>
            <template v-else>
              <el-input v-model="row[`${col.prop}${editKey}`]" 
                        :placeholder="`${col.label}`" 
                        type="number" 
                        @input="handleInputChange(row,col)" 
                        class="noPaddingInput">
              </el-input>
            </template>
          </template>
        </el-table-column>
      </template> -->

      <!-- 修改来源字段的模板，确保显示中文 -->
      <template #type="{ col }">
        <el-table-column v-bind="col">
          <template #default="{row}">
            <span>
              {{ 
                row.type === 'wb' ? '外部' : 
                row.type === 'zsmy' ? '掌上煤源' : 
                row.type === 'nb' ? '内部' : 
                row.source || row.type || '未知'
              }}
            </span>
          </template>
        </el-table-column>
      </template>

    </SnProTable>

    <FormModal v-if="addInfo.visitable" v-model="addInfo.visitable" :model="model"
               :categoryNamesList="categoryNamesList.map(v=>v.name)" :optName="addInfo.optName" :coalSource="coalSource"
               :record="addInfo.record" @ok="() => {
                    getCategoryNamesList()
                    getList(true)
                }" />

    <AvgFormModal v-if="avgAddInfo.visitable" v-model="avgAddInfo.visitable" :model="model"
                  :categoryNamesList="categoryNamesList.map(v=>v.name)" :optName="avgAddInfo.optName" :coalSource="coalSource"
                  :record="avgAddInfo.record" @ok="() => getList(true)" />

    <FieldUpdateFormModal v-if="fieldUpdateInfo.visitable" v-model="fieldUpdateInfo.visitable" :model="model"
                          :coalSource="coalSource" :optName="fieldUpdateInfo.optName" :record="fieldUpdateInfo.record"
                          @ok="() => getList(true)" />

    <ItemCoal v-if="addVisible" :add-visible.sync="addVisible" :details="coalDetail"
              :categoryNamesList="categoryNamesList.map(v=>v.name)" :model="model" :dialogStatus="dialogStatus" @closeVisible="()=>{
                    addVisible = false
                    getList(false)
                }">
    </ItemCoal>

  </div>

</template>

<script>
import TipModal from '@/utils/modal'
import model, { getEditCols } from './model'
import FormModal from './components/FormModal.vue'
import AvgFormModal from './components/AvgFormModal.vue'
import FieldUpdateFormModal from './components/FieldUpdateFormModal.vue'
import { getDate } from '@/utils/dateUtils'
import SnProTable from '@/components/Common/SnProTable/index.vue'
import checkPermission from '@/utils/permission'
import { COAL_SOURCE_TYPE, MERGE_TYPE } from '@/const'
import CalcUtils from '@/utils/calcUtils'
import SnSimpleSelect from "@/components/Common/SnSimpleSelect/index.vue";
import { listCategoryNames } from "@/api/common/listAllSimple";
import ItemCoal from './components/ItemCoal.vue'
import { getRangeListApi } from "@/api/coal";

export default {
  name: 'OutSideStock',
  components: {
    SnSimpleSelect,
    SnProTable,
    FormModal,
    FieldUpdateFormModal,
    AvgFormModal,
    ItemCoal
  },
  data() {
    return {
      addVisible: false,
      coalDetail: {},
      dialogStatus: 'update',

      model,
      MERGE_TYPE,
      pageRef: 'page',
      permissions: {
        save: '*',
        remove: '*'
      },
      addInfo: {
        optName: 'create',
        visitable: false,
        record: {}
      },

      avgAddInfo: {
        optName: 'create',
        visitable: false,
        record: {}
      },

      fieldUpdateInfo: {
        optName: 'create',
        visitable: false,
        record: {}
      },

      editKey: '_edit',
      editPropList: getEditCols(),
      categoryNamesList: [],

      // 添加密度区间范围数组
      // densityRanges: [
      //   '0.25-0.30', '0.30-0.35', '0.35-0.40', '0.40-0.45', '0.45-0.50',
      //   '0.50-0.55', '0.55-0.60', '0.60-0.65', '0.65-0.70', '0.70-0.75',
      //   '0.75-0.80', '0.80-0.85', '0.85-0.90', '0.90-0.95', '0.95-1.00',
      //   '1.00-1.05', '1.05-1.10', '1.10-1.15', '1.15-1.20', '1.20-1.25',
      //   '1.25-1.30', '1.30-1.35', '1.35-1.40', '1.40-1.45', '1.45-1.50',
      //   '1.50-1.55', '1.55-1.60', '1.60-1.65', '1.65-1.70', '1.70-1.75',
      //   '1.75-1.80', '1.80-1.85', '1.85-1.90', '1.90-1.95', '1.95-2.00',
      //   '2.00-2.05', '2.05-2.10', '2.10-2.15', '2.15-2.20', '2.20-2.25',
      //   '2.25-2.30', '2.30-2.35', '2.35-2.40', '2.40-2.45', '2.45-2.50',
      //   '2.50-2.55', '2.55-2.60'
      // ]
    }
  },
  created() {
    this.getDataList()
    this.getCategoryNamesList()
  },
  props: {
    coalSource: {
      type: String,
      default: COAL_SOURCE_TYPE.ZI_YOU
    }
  },
  computed: {
    getColSlots() {
      const exclude = ['activePriceFactoryPriceNoTax', 'reduceMtFactoryPriceNoTax', 'vd', 'mci', 'name']
      return this.editPropList.filter((item) => !exclude.includes(item.prop))
    },
    getEditList() {
      return [...this.editPropList]
    },
    actions() {
      return [
        {
          type: 'add',
          text: '新增',
          hasPermission: '*',
          onClick: async (item) => {
            this.setModal('addInfo')
          }
        },
        // {
        //   type: 'add',
        //   text: '合并数据',
        //   hasPermission: [this.permissions.save],
        //   onClick: async (item) => {
        //     let data = this.$refs[this.pageRef].getSelectRows()
        //     if (data.length) {
        //       this.setModal('avgAddInfo', 'update', {
        //         data: data
        //       })
        //     } else {
        //       TipModal.msgWarning('请先选择要操作的数据')
        //     }
        //   }
        // },
        // {
        //   type: 'add',
        //   text: '批量编辑',
        //   hasPermission: [this.permissions.save],
        //   onClick: async (item) => {
        //     let data = this.$refs[this.pageRef].getSelectRows()
        //     if (data.length) {
        //       data = data.map((item) => {
        //         const obj = { ...item }
        //         for (const [k, v] of Object.entries(obj)) {
        //           if (v === null || v === '/') obj[k] = undefined
        //         }
        //         return obj
        //       })
        //       this.setModal('addInfo', 'update', {
        //         data: data
        //       })
        //     } else {
        //       TipModal.msgWarning('请先选择要操作的数据')
        //     }
        //   }
        // },

        // // 批量改
        // {
        //   type: 'add',
        //   text: '改折水/路耗/运费',
        //   hasPermission: [this.permissions.save],
        //   onClick: async (item) => {
        //     let data = this.$refs[this.pageRef].getSelectRows()
        //     if (data.length) {
        //       this.setModal('fieldUpdateInfo', 'update', {
        //         data: data.map(v => {
        //           return {
        //             id: v.id,
        //             reduceMtStandard: v.reduceMtStandard,
        //             activePriceTransportCostP1: v.activePriceTransportCostP1,
        //             activePriceTransportPriceNoTax: v.activePriceTransportPriceNoTax,
        //             coalSource: v.coalSource,
        //             coalName: v.coalName,
        //           }
        //         })
        //       })
        //     } else {
        //       TipModal.msgWarning('请先选择要操作的数据')
        //     }
        //   }
        // },


      ]
    },

  },
  methods: {
    editName(row) {

      this.addVisible = true
      this.coalDetail = { ...row }
    },
    querySearch(queryString, cb) {
      let restaurants = this.categoryNamesList;
      let results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants;
      // 调用 callback 返回建议列表的数据
      cb(results);
    },
    createFilter(queryString) {
      return (restaurant) => {
        return (restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0);
      };
    },

    async getCategoryNamesList() {
      try {
        const { data } = await listCategoryNames({ coalSource: this.coalSource })
        this.categoryNamesList = data.map(v => {
          return {
            name: v,
            id: v,
            value: v
          }
        })

      } catch (e) {
        this.categoryNamesList = []
      }
    },
    async getDataList() {
      try {
        const { data } = await getRangeListApi();
        this.rangeList = data
      } catch (e) {

        this.rangeList = []
      }
    },
    handleInputChange(row, col) {
      const prop = col.prop
      const getEditVal = (key) => {
        const value = row[`${key}${this.editKey}`]
        return [undefined, null, ''].includes(value) ? undefined : Number(value)
      }
      const set = (key, value) => {
        row[`${key}${this.editKey}`] = value
      }
      const isEmpty = (val) => ['', null, undefined].includes(val)

      // 处理密度区间的输入
      if (/^\d+\.\d+-\d+\.\d+$/.test(prop)) {
        const value = getEditVal(prop);
        // 验证输入是否为有效的百分比值(0-100)
        if (value !== undefined && (value < 0 || value > 100)) {
          TipModal.msgWarning(`${prop}的值应在0到100之间`);
          set(prop, value < 0 ? 0 : (value > 100 ? 100 : value));
        }
        return;
      }

      const v = {
        activePriceCoalPriceWithTax: getEditVal('activePriceCoalPriceWithTax'),
        activePriceTransportPriceNoTax: getEditVal('activePriceTransportPriceNoTax'),
        activePriceTransportCostP1: getEditVal('activePriceTransportCostP1'),
        activePriceCoalPriceNoTax: getEditVal('activePriceCoalPriceNoTax'),
        mt: getEditVal('mt'),
        reduceMtStandard: getEditVal('reduceMtStandard'),
        factoryPrice: getEditVal('factoryPrice'),

        ad: getEditVal('ad'),
        vdaf: getEditVal('vdaf'),
        siO2: getEditVal('siO2'),
        al2O3: getEditVal('al2O3'),
        fe2O3: getEditVal('fe2O3'),
        caO: getEditVal('caO'),
        k2O: getEditVal('k2O'),
        na2O: getEditVal('na2O'),
        mgO: getEditVal('mgO'),
        tiO2: getEditVal('tiO2'),
        mnO2: getEditVal('mnO2'),
      }

      // 含税煤价
      const taxPriceEffect = () => set('activePriceFactoryPriceNoTax', CalcUtils.calculateTaxPrice(v.activePriceCoalPriceWithTax, v.activePriceTransportPriceNoTax, v.activePriceTransportCostP1))
      // 不含税煤价
      const noTaxPriceEffect = () => set('activePriceFactoryPriceNoTax', CalcUtils.calculateNoTaxPrice(v.activePriceCoalPriceNoTax, v.activePriceTransportPriceNoTax, v.activePriceTransportCostP1))

      if (['activePriceCoalPriceNoTax', 'activePriceCoalPriceWithTax', 'activePriceTransportPriceNoTax', 'activePriceTransportCostP1'].includes(prop)) {
        if (!isEmpty(v.activePriceCoalPriceWithTax) && !isEmpty(v.activePriceCoalPriceNoTax)) { // 如果都不位空则默认使用不含税价格算
          noTaxPriceEffect()
        } else if (isEmpty(v.activePriceCoalPriceWithTax)) {
          noTaxPriceEffect()
        } else if (isEmpty(v.activePriceCoalPriceNoTax)) {
          taxPriceEffect()
        }
      }


      set('vd', CalcUtils.calculateVd(v))


      set('mci', CalcUtils.calculateMCI({
        ...v,
        vd: getEditVal('vd')
      }))


      set('reduceMtFactoryPriceNoTax', CalcUtils.calculateRemoveWaterNoTaxPrice(v.mt, v.reduceMtStandard, getEditVal('activePriceFactoryPriceNoTax')))

      if (prop !== 'coalBlendingCost') set('coalBlendingCost', getEditVal('reduceMtFactoryPriceNoTax'))

      // 处理含税价格字段变更
      if (prop === 'factoryPrice') {
        console.log('处理factoryPrice字段变更:', v.factoryPrice);
        
        // 当用户直接编辑含税价格时，设置activePriceCoalPriceWithTax的值
        set('activePriceCoalPriceWithTax', v.factoryPrice);
        console.log('设置activePriceCoalPriceWithTax为:', v.factoryPrice);
        
        // 同时更新不含税煤价
        if (!isEmpty(v.factoryPrice)) {
          const noTaxPrice = CalcUtils.getShowValue(CalcUtils.divide(v.factoryPrice, 1.13), 2);
          set('activePriceCoalPriceNoTax', noTaxPrice);
          console.log('设置activePriceCoalPriceNoTax为:', noTaxPrice);
          
          // 更新厂内价格
          noTaxPriceEffect();
        }
        
        // 打印更新后的状态
        console.log('更新后的字段值:', {
          factoryPrice: row[`factoryPrice${this.editKey}`],
          activePriceCoalPriceWithTax: row[`activePriceCoalPriceWithTax${this.editKey}`],
          activePriceCoalPriceNoTax: row[`activePriceCoalPriceNoTax${this.editKey}`]
        });
      }

    },
    handleClickRow(row, column, event) {
      this.handleRow(row, 'edit', {
        $index: event.$index
      })
    },

    getShowValue: CalcUtils.getShowValue,
    checkPermission,
    setModal(target, optName = 'create', record = {}) {
      this[target].visitable = true
      this[target].optName = optName
      if (optName) this[target].record = { ...record }
    },
    afterFetch(data, listKey) {
      data[listKey].forEach(item => {
        item.sourceItem = { ...item }
        item.loading = false
        item.isEdit = false
        this.getEditList.forEach((value) => {
          item[`${value.prop}${this.editKey}`] = item[value.prop]
        })

        // 处理煤岩含量数据
        if (item.coalRockContent && typeof item.coalRockContent === 'string') {
          try {
            const rockContentData = JSON.parse(item.coalRockContent);
            if (Array.isArray(rockContentData)) {
              rockContentData.forEach(rockItem => {
                if (rockItem.rangeName && rockItem.proportion !== undefined) {
                  // 设置对应密度区间的值
                  item[rockItem.rangeName] = rockItem.proportion;
                  item[`${rockItem.rangeName}${this.editKey}`] = rockItem.proportion;
                }
              });
            }
          } catch (e) {
            console.error('解析煤岩含量数据失败:', e);
          }
        }
      })

      console.log(data[listKey], 'list')

      return { ...data, [listKey]: data[listKey] }
    },

    getDate,
    rowClassName({ row, rowIndex, columnIndex, column }) {
      if (rowIndex === 0 && row.base === 1) {
        return 'mark-base-row'
      }
    },
    beforeFetch(params) {
      return {
        ...params,
        coalSource: this.coalSource
      }
    },
    async handleSortTop(row) {
      try {
        console.log('OutSideStock置顶操作完整参数:', row);
        
        // 识别数据来源：优先使用type属性，其次使用coalSource属性
        let dataSource;
        if (row.type === 'zsmy') {
          dataSource = 'zsmy'; 
        } else if (row.type === 'wb') {
          dataSource = row.type;
        } else if (row.coalSource) {
          dataSource = row.coalSource;
        } else {
          dataSource = this.coalSource;
        }
        
        console.log('使用的数据源标识:', dataSource);
        
        // 添加加载状态反馈
        this.$message({
          message: '正在处理置顶操作...',
          type: 'info',
          duration: 1000
        });
        
        const status = await this.model.sortTop({
          id: row.id,
          sort: row.sort,
          coalSource: dataSource
        });
        
        if (status) {
          TipModal.msgSuccess(`${row.sort ? '取消置顶' : '置顶'}操作成功`);
          this.getList(false);
        } else {
          TipModal.msgError(`${row.sort ? '取消置顶' : '置顶'}操作失败`);
        }
      } catch (error) {
        console.error('置顶操作异常:', error);
        TipModal.msgError(`操作失败: ${error.message || '未知错误'}`);
      }
    },

    async handleRemove({ id, coalSource, mergeType, type }) {
      try {
        // 优先使用type字段识别掌上煤源
        const sourceType = type || coalSource;
        
        console.log('删除参数:', { id, coalSource, type: sourceType, mergeType });
        
        const resp = await this.model.remove({ 
          id, 
          coalSource, 
          useConfirm: true, 
          type: type || mergeType 
        })
        
        if (resp) {
          TipModal.msgSuccess(`删除成功`)
          this.getList(true)
        }
      } catch (error) {
        console.error('删除失败:', error);
        TipModal.msgError(`删除失败: ${error.message || '未知错误'}`);
      }
    },

    async handleRow(row, type, ...rest) {
      if (type === 'edit') {
        row.isEdit = true
      }
      if (type === 'save') {
        row.loading = true
        const rowIndex = rest[0]['$index'] + 1
        try {
          // 获取煤岩含量数据列表
          const rockList = await this.buildRockList(row);
          
          // 准备提交的数据
          const resultRow = { ...row, rockList };
          
          // 打印保存前的原始数据
          console.log('保存前的原始数据:', {
            factoryPrice: row.factoryPrice,
            'factoryPrice_edit': row['factoryPrice_edit'],
            activePriceCoalPriceWithTax: row.activePriceCoalPriceWithTax,
            'activePriceCoalPriceWithTax_edit': row['activePriceCoalPriceWithTax_edit']
          });
          
          // 同步编辑值到主要数据
          this.getEditList.forEach((value) => {
            resultRow[value.prop] = resultRow[`${value.prop}${this.editKey}`]
          });
          
          // 添加日志跟踪factoryPrice的值
          console.log('编辑前的factoryPrice值:', row.factoryPrice);
          console.log('编辑后的factoryPrice值:', row['factoryPrice_edit']);
          console.log('最终提交的factoryPrice值:', resultRow.factoryPrice);
          
          // 确保factoryPrice值被正确设置
          if (row['factoryPrice_edit'] !== undefined) {
            resultRow.factoryPrice = row['factoryPrice_edit'];
            // 同时更新含税煤价
            resultRow.activePriceCoalPriceWithTax = row['factoryPrice_edit'];
            
            // 计算不含税煤价
            if (resultRow.factoryPrice) {
              resultRow.activePriceCoalPriceNoTax = CalcUtils.getShowValue(
                CalcUtils.divide(resultRow.factoryPrice, 1.13), 2
              );
            }
          }
          
          // 确保API请求包含factoryPrice字段
          if (!resultRow.factoryPrice && resultRow.activePriceCoalPriceWithTax) {
            resultRow.factoryPrice = resultRow.activePriceCoalPriceWithTax;
          }

          // 打印最终提交的数据
          console.log('最终提交的数据:', {
            factoryPrice: resultRow.factoryPrice,
            activePriceCoalPriceWithTax: resultRow.activePriceCoalPriceWithTax,
            activePriceCoalPriceNoTax: resultRow.activePriceCoalPriceNoTax
          });

          // 同步密度区间的值
          Object.keys(row).forEach(key => {
            if (/^\d+\.\d+-\d+\.\d+$/.test(key) && !key.endsWith(this.editKey)) {
              resultRow[key] = row[`${key}${this.editKey}`] !== undefined ? 
                row[`${key}${this.editKey}`] : row[key];
            }
          });

          console.log(resultRow, 'res')

          const resp = await this.model.update({ data: [resultRow] })
          if (resp) {
            TipModal.msgSuccess(`第${rowIndex}行编辑成功。`)
            // 刷新界面
            this.getList(true)
          }
        } catch (e) {
          console.log(e, 'e')
        }
        row.loading = false
        row.isEdit = false
      }

      if (type === 'cancel') {
        this.getEditList.forEach((value) => {
          row[`${value.prop}${this.editKey}`] = row[value.prop]
        })
        row.isEdit = false
      }
    },

    async buildRockList(row) {
      try {
        // 获取所有密度区间的列
        const densityRangeProps = Object.keys(row).filter(key => 
          /^\d+\.\d+-\d+\.\d+$/.test(key) && !key.endsWith(this.editKey)
        );
        
        // 收集所有有值的密度区间数据
        return densityRangeProps.map(rangeName => {
          const proportion = row[`${rangeName}${this.editKey}`] !== undefined ? 
            row[`${rangeName}${this.editKey}`] : 
            row[rangeName];
            
          return {
            rangeName,
            proportion: proportion !== undefined ? Number(proportion) : undefined
          };
        }).filter(item => item.proportion !== undefined);
      } catch (e) {
        console.log(e, 'buildRockList')
        return []
      }
    },

    getList(clear = false) {
      if (clear) {
        this.$refs[this.pageRef].clearSelect(true)
      } else {
        this.$refs[this.pageRef].getList()
      }
    },

    async handleRefreshCoalQuality(row) {
      try {
        // 确认对话框
        await this.$confirm('是否确认刷新指标', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true,
          iconClass: 'el-icon-warning-outline'
        });
        
        // 用户确认后执行刷新
        row.loading = true;
        const resp = await this.model.refreshCoalQuality({
          id: row.id
        });
        
        if (resp) {
          TipModal.msgSuccess('煤质数据刷新成功');
          this.getList(true);
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('刷新煤质数据失败:', error);
          TipModal.msgError('刷新煤质数据失败');
        }
      } finally {
        if (row.loading) {
          row.loading = false;
        }
      }
    },

    showCoalDetail(row) {
      if (!row || !row.id) {
        TipModal.msgWarning('无效的煤炭数据');
        return;
      }
      
      // 显示加载提示
      this.$message({
        message: '正在加载煤炭详情...',
        type: 'info',
        duration: 1000
      });
      
      // 根据煤源类型确定正确的type参数
      let sourceType = 'wb'; // 默认为外部煤源
      
      if (row.type === 'nb') {
        sourceType = 'nb'; // 内部煤源
      } else if (row.type === 'zsmy') {
        sourceType = 'zsmy'; // 掌上煤焦
      } else if (row.type === 'wb') {
        sourceType = 'wb'; // 外部煤源
      }
      
      console.log('煤源类型:', sourceType, '煤炭ID:', row.id, '产地:', row.location);
      
      // 先预填充基础数据
      this.coalDetail = {
        ...row,
        type: sourceType,
        // 确保基础信息存在
        basicinformation: [{
          coalName: row.coalName || '',
          coalCategoryName: row.coalCategoryName || '',
          location: row.location || '',
          factoryPrice: row.factoryPrice || '',
          activePriceTransportPriceNoTax: row.activePriceTransportPriceNoTax || '',
          coalBlendingCost: row.coalBlendingCost || ''
        }],
        // 确保指标数据存在
        indexlist: [{
          ad: row.ad || '',
          std: row.std || '',
          vdaf: row.vdaf || '',
          g: row.g || '',
          y: row.y || '',
          x: row.x || '',
          mt: row.mt || '',
          macR0: row.macR0 || '',
          macS: row.macS || '',
          cri: row.cri || '',
          csr: row.csr || ''
        }],
        // 确保煤灰成分数据存在
        siO2: row.siO2 || '',
        al2O3: row.al2O3 || '',
        fe2O3: row.fe2O3 || '',
        caO: row.caO || '',
        mgO: row.mgO || '',
        na2O: row.na2O || '',
        k2O: row.k2O || '',
        tiO2: row.tiO2 || '',
        p2O5: row.p2O5 || '',
        so3: row.so3 || '',
        mnO2: row.mnO2 || '',
        // 添加一个标记，表示数据已经预处理，避免ItemCoal组件中重复请求
        _dataPreprocessed: true,
        // 添加一个标记，表示这是从列表点击进入的详情页
        _fromListClick: true
      };
      
      // 设置对话框状态
      this.dialogStatus = 'update';
      
      // 在显示弹窗前先获取完整数据，避免组件内部重复请求
      this.model.get({
        id: row.id,
        type: sourceType // 使用type参数
      }).then(response => {
        if (!response || !response.data) {
          console.warn('获取煤炭详情返回空数据');
          // 即使获取失败也显示弹窗，使用预填充的数据
          this.addVisible = true;
          return;
        }
        
        console.log('获取煤炭详情成功:', response);
        
        // 更新详情数据
        const detailData = response.data;
        
        // 处理煤岩含量数据
        if (detailData.coalRockContent && typeof detailData.coalRockContent === 'string') {
          try {
            const rockContentData = JSON.parse(detailData.coalRockContent);
            if (Array.isArray(rockContentData)) {
              // 对于掌上煤源类型，处理反射率数据，将0值标记为特殊值
              if (sourceType === 'zsmy') {
                rockContentData.forEach(item => {
                  if (item.proportion === 0 || item.proportion === '0' || 
                      item.proportion === 0.0 || item.proportion === '0.0' || 
                      item.proportion === 0.00 || item.proportion === '0.00' ||
                      item.proportion === '' || item.proportion === null || item.proportion === undefined) {
                    item.proportion = '***';
                    item._isZeroValue = true; // 添加标记，表示这是一个需要特殊处理的零值
                  }
                });
              }
              
              detailData.coalRockList = rockContentData;
              // 同时构建rangeValues数据，避免ItemCoal组件中重复处理
              detailData.rangeValues = rockContentData.map(item => ({
                rangeName: item.rangeName,
                proportion: item.proportion,
                _isZeroValue: item._isZeroValue // 传递标记
              }));
            }
          } catch (e) {
            console.error('解析煤岩含量数据失败:', e);
          }
        }
        
        // 如果有proportionContent和rangeContent，构建rangeValues
        if (!detailData.rangeValues && detailData.proportionContent && detailData.rangeContent) {
          try {
            const proportionValues = typeof detailData.proportionContent === 'string' ? 
              JSON.parse(detailData.proportionContent) : detailData.proportionContent;
            const rangeNameValues = typeof detailData.rangeContent === 'string' ? 
              JSON.parse(detailData.rangeContent) : detailData.rangeContent;
            
            if (Array.isArray(proportionValues) && Array.isArray(rangeNameValues)) {
              detailData.rangeValues = proportionValues.map((proportion, index) => {
                // 对于掌上煤源类型，处理反射率数据，将0值标记为特殊值
                let propValue = proportion || 0;
                let isZeroValue = false;
                
                if (sourceType === 'zsmy' && 
                    (propValue === 0 || propValue === '0' || 
                     propValue === 0.0 || propValue === '0.0' || 
                     propValue === 0.00 || propValue === '0.00' ||
                     propValue === '' || propValue === null || propValue === undefined)) {
                  propValue = '***';
                  isZeroValue = true;
                }
                
                return {
                  rangeName: rangeNameValues[index] || '',
                  proportion: propValue,
                  _isZeroValue: isZeroValue // 添加标记
                };
              });
            }
          } catch (e) {
            console.error('解析反射率数据失败:', e);
          }
        }
        
        // 更新详情数据
        this.coalDetail = {
          ...this.coalDetail,
          ...detailData,
          type: sourceType, // 确保类型不被覆盖
          _dataPreprocessed: true, // 标记数据已经预处理
          _fromListClick: true // 标记这是从列表点击进入的详情页
        };
        
        console.log('更新后的详情数据:', this.coalDetail);
        
        // 数据准备完成后显示弹窗
        this.addVisible = true;
      }).catch(error => {
        console.error('获取煤炭详情失败:', error);
        TipModal.msgError('获取煤炭详情失败，请检查API响应');
        // 即使获取失败也显示弹窗，使用预填充的数据
        this.addVisible = true;
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.noPaddingInput {
  padding: 0 5px;

  ::v-deep {
    .el-input__inner {
      padding: 0px 6px !important;
      text-align: center;
      border: 1px solid #256ddf;
    }
  }
}

.tag-left-right {
  font-size: 10px;
  position: absolute;
  right: -35px;
  text-align: center;
  top: -8px;
  width: 76px;
  line-height: 42px;
  height: 28px;
  z-index: 999;
  color: #fff;
  background: #ff9639;
  transform: rotate(50deg);
}

::v-deep {
  .el-divider--vertical {
    margin: 0 5px !important;
  }

  .el-table {
    .el-table__header {
      .cell {
        padding-right: 2px;
        padding-left: 2px;
      }
    }

    .el-table__row {
      .cell {
        min-height: 28px;

        line-height: 28px;
        padding-right: 0;
        padding-left: 0;
      }
    }
  }
}
</style>
