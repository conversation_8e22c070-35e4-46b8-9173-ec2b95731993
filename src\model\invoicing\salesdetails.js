import Model from '@/s-curd/src/model'
import fetch from '@/utils/request'
const name = `/cwe/a/weightHouseOut`
class _Model extends Model {
    constructor() {
        const dataJson = {
            date: (new Date(new Date().getTime() - 24 * 60 * 60 * 1000)).Format('yyyy-MM-dd'), // 日期
            name: '', // 品名,
            contractCode: '',
            contractId: '',
            customerId: '',
            customerName: '',
            senderName: '', // 发货单位
            receiverName: '', // 收货单位
            coalType: '', // 煤种
            truckCount: '', // 车数
            sendWeight: '', // 原发数
            receiveWeight: '', // 实收数
            wayCost: '', // 途耗
            truckCountAcc: '', // 车数累计
            sendWeightAcc: '', // 原发数累计
            receiveWeightAcc: '', // 实收数累计
            wayCostAcc: '', // 途耗累计
            // stationMan: '', // 在岗人
            // transferMan: '', // 交班人
            // nextMan: '', // 接班人
            // remarks: '', // 备注信息
            firstWeight: '', // 毛重
            secondWeight: '', // 皮重
            productCode: '',
            productId: ''
        }
        const form = {}
        const tableOption = {
            // showIndex: true,
            showPage: true,
            showSelection: true,
            columns: [
                {
                    label: '日期',
                    prop: 'date',
                    sortable: true,
                    isShow: true,
                    width: 90,
                    align: "center"
                },
                {
                    label: '品名',
                    prop: 'name',
                    slot: 'name',
                    isShow: true
                },
                {
                    label: '收货单位',
                    // prop: 'customerName',
                    prop: 'receiverName',
                    isShow: true,
                    width: 100,
                    align: "center"
                },
                {
                    label: '车数',
                    prop: 'truckCount',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '原发数',
                    prop: 'sendWeight',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '实收数',
                    prop: 'receiveWeight',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '亏吨',
                    prop: 'lossNull',
                    isShow: true,
                    align: "center"
                },
                // {
                //     label: '结算数',
                //     prop: 'settlement',
                //     isShow: true,
                //     align: "center"
                // },
                // {
                //     label: '品名',
                //     prop: 'name',
                //     slot: 'name',
                //     isShow: true
                // },

                {
                    label: '合同名称',
                    prop: 'contractName',
                    slot: 'contractName',
                    isShow: true,
                    align: 'center',
                },

                {
                    label: '合同编号',
                    prop: 'contractId',
                    slot: 'contractId',
                    isShow: true,
                    noExport: true,
                },
                {
                    label: '合同编号',
                    prop: 'contractCode',
                    isShow: true,
                    slot: "contractCode"
                },
                {
                    label: '运费',
                    prop: 'carriage',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '煤价',
                    prop: 'price',
                    slot: 'price',
                    isShow: true
                },
                {
                    label: '是否含税',
                    prop: 'hasFax',
                    slot: 'hasFax',
                    isShow: true,
                },
                {
                    label: '买方',
                    prop: 'firstParty',
                    isShow: true,
                    width: 170,
                    align: "center"
                },
                {
                    label: '卖方',
                    prop: 'secondParty',
                    isShow: true,
                    width: 170,
                    align: "center"
                },

                {
                    label: '发货单位',
                    prop: 'senderName',
                    isShow: true,
                    width: 100,
                    align: "center"
                },

                // {
                //     label: '运费',
                //     prop: 'carriage',
                //     slot: 'carriage',
                //     isShow: true,
                // },

                // {
                //     label: '水分',
                //     prop: 'mt',
                //     slot: 'mt',
                //     isShow: true,
                // },

                // {
                //     label: '煤种',
                //     prop: 'coalType',
                //     isShow: true
                // },
                // {
                //     label: '车数',
                //     prop: 'truckCount',
                //     isShow: true
                // },

                {
                    label: '实收日期',
                    prop: 'actualReceiveDate',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '途耗',
                    prop: 'wayCost',
                    slot: 'wayCost',
                    isShow: true,
                    align: "center"
                },
                {
                    label: '车牌',
                    prop: 'plateNumber',
                    isShow: true,
                    width: 200,
                    align: "center"
                },


                // {
                //     label: '车数累计',
                //     prop: 'truckCountAcc',
                //     isShow: true
                // },
                // {
                //     label: '原发累计(本月)',
                //     prop: 'sendWeightAcc',
                //     isShow: true,
                //     width: 120
                // },
                // {
                //     label: '实收累计(本月)',
                //     prop: 'receiveWeightAcc',
                //     isShow: true,
                //     width: 120
                // },
                // {
                //     label: '途耗累计',
                //     prop: 'wayCostAcc',
                //     slot: 'wayCostAcc',
                //     isShow: true
                // },
                // {
                //     label: '在岗人',
                //     prop: 'stationMan',
                //     isShow: true
                // },
                // {
                //     label: '交班人',
                //     prop: 'transferMan',
                //     isShow: true
                // },
                // {
                //     label: '接班人',
                //     prop: 'nextMan',
                //     isShow: true
                // },
                {
                    label: '一次计量时间',
                    prop: 'firstWeightDate',
                    slot: 'firstWeightDate',
                    isShow: true,
                    sortable: true,
                    width: 180,
                    align: "center"
                },
                {
                    label: '二次计量时间',
                    prop: 'secondWeightDate',
                    slot: 'secondWeightDate',
                    isShow: true,
                    sortable: true,
                    width: 180,
                    align: "center"
                },
                {
                    label: '毛重',
                    prop: 'firstWeight',
                    slot: 'firstWeight',
                    isShow: true
                },
                {
                    label: '皮重',
                    prop: 'secondWeight',
                    slot: 'secondWeight',
                    isShow: true
                },
                {
                    label: '备注',
                    prop: 'remarks',
                    slot: 'remarks',
                    isShow: true
                },
                {
                    noExport: true,
                    label: '操作',
                    prop: 'opt',
                    slot: 'opt',
                    isShow: true
                }
            ],
            // sumIndex: 5,
            sumText: '合计',
            summaries: [
                { prop: 'truckCount', suffix: '', fixed: 0, avg: false },
                { prop: 'sendWeight', suffix: '', fixed: 2, avg: false },
                { prop: 'receiveWeight', suffix: '', fixed: 2, avg: false },
                // { prop: 'price', suffix: '', fixed: 2 },
                // { prop: 'mt', suffix: '', fixed: 2 },
                // { prop: 'carriage', suffix: '', fixed: 2 }

                { prop: 'lossNull', suffix: '', fixed: 2, avg: false },
                { prop: 'price', suffix: '', fixed: 2, avg: false },
                { prop: 'mt', suffix: '', fixed: 2, avg: false },
                { prop: 'carriage', suffix: '', fixed: 2, avg: false },
                // { prop: 'wayCost', suffix: '', fixed: 2, avg: true },

                // { prop: 'wayCost', suffix: '%', fixed: 2 },
                // { prop: 'truckCountAcc', suffix: '', fixed: 0 },
                // { prop: 'sendWeightAcc', suffix: '', fixed: 2 },
                // { prop: 'receiveWeightAcc', suffix: '', fixed: 2 },
                // { prop: 'wayCostAcc', suffix: '%', fixed: 2 }
            ]
        }
        super(name, dataJson, form, tableOption)
    }

    save(data) {
        return fetch({
            url: `${name}/save`,
            method: 'post',
            data
        })
    }
    // page(searchForm) {
    //     return fetch({
    //         url: `${name}/page`,
    //         method: 'get',
    //         params: searchForm
    //     })
    // }
    async page(params = {}, normal = false) {
        // params.orderBy = 'secondWeightDate'
        try {
            const res = await fetch({
                url: `${name}/page`,
                method: 'get',
                params: params
            })
            if (!normal) this.formatRecords({ records: res.data.records, fields: ['price', 'contractId', 'remarks'] })
            return res
        } catch (e) {
            // console.log(e)
        }
    }
    formatRecords({ records, fields }) {
        records.forEach(item => {
            item._all = false
            item.active = {}
            item.bak = {}
            for (const field of fields) {
                item.active[field] = false
                item.bak[field] = item[field]
            }
        })
        return records
    }
    getAccValues(params) {
        return fetch({
            url: `${name}/getAccValues`,
            method: 'get',
            params
        })
    }

    deleteId(id) {
        return fetch({
            url: this.name + '/deleteById',
            method: 'post',
            data: { id }
        })
    }
    //批量修改煤价
    savebatchChangePrice(data) {
        return fetch({
            url: `${name}/updatePrice`,
            method: 'post',
            data
        })
    }
    //批量修改运费
    savebatchChangeCarriage(data) {
        return fetch({
            url: `${name}/updateCarriage`,
            method: 'post',
            data
        })
    }
    //批量修改合同
    savebatchChangeContract(data) {
        return fetch({
            url: `${name}/updateContract`,
            method: 'post',
            data
        })
    }
    //批量修改水分
    savebatchChangeMt(data) {
        return fetch({
            url: `${name}/updateMt`,
            method: 'post',
            data
        })
    }
    //批量修改备注
    savebatchChangeRemarks(data) {
        return fetch({
            url: `${name}/updateRemarks`,
            method: 'post',
            data
        })
    }
    refresh(data) {
        return fetch({
            url: `${name}/refreshByDate`,
            method: 'post',
            data
        })
    }
}

export default new _Model()
