<template>
  <el-dialog :append-to-body="isSourceWash" :before-close="()=>handleClose('full')" :title="details.name || '新增'"
             :visible.sync="visible" class="add"
             top="3vh" width="80%">
    <div class="coal">
      <el-form ref="entityForm" :model="entityForm" label-width="80px">
        <indicators-card>
          <template #title>
            <div class="title-content">
              <span>基础信息</span>
              <!-- <el-button type="primary">保存
              </el-button> -->
            </div>
          </template>
          <!-- <el-row>
            <el-col :span="6">
              <el-form-item label="名称" prop="name">
                <el-input v-model="entityForm.name" :rules="[{ required: true, message: '请输入名称' }]"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="煤种" prop="type" :rules="[{ required: true, message: '请选择一项' }]">
                <dict-select style="width:100%" v-model="entityForm.type" type="coal_type"></dict-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="省市区" prop="province" :rules="[{ required: true, message: '请输入省市' }]">
                <RegionSelectOld :value="region" @input="value=>((entityForm.location=value.location)||1)&&(region=value.val)">
                </RegionSelectOld>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item prop="batchCode" label="批次号" :rules="[{ required: true, message: '请输入批次号' }]">
                <el-input :value="entityForm.batchCode" type="number"></el-input>
              </el-form-item>
            </el-col>
          </el-row> -->

          <!-- <el-row type="flex" :gutter="50">
            <el-col>
              <el-table :data="entityForm.Basicinformation" stripe :header-cell-style="headClass">
                <el-table-column label="名称" align="center">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.name"></el-input>
                  </template>
                </el-table-column>
                <el-table-column label="煤种" align="center">
                  <template slot-scope="scope">
                    <dict-select style="width:100%" v-model="scope.row.type" type="coal_type"></dict-select>
                  </template>
                </el-table-column>
                <el-table-column label="省市区" align="center">
                  <template slot-scope="scope">
                    <RegionSelectOld :value="region" @input="value=>((scope.row.location=value.location)||1)&&(region=value.val)">
                    </RegionSelectOld>
                  </template>
                </el-table-column>
                <el-table-column label="批次号" align="center">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.batchCode"></el-input>
                  </template>
                </el-table-column>
                <el-table-column label="到厂价" align="center">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.arrivePrice"></el-input>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row> -->

          <el-col v-if="entityForm.Basicinformation">
            <EditTable ref="editTable" v-model="entityForm.Basicinformation" :columns="getColumnsInfo"
                       :rowHeaders="true"
                       @inited="handleEditTableInitInfo">
            </EditTable>
          </el-col>
        </indicators-card>

        <indicators-card title="指标">
          <!-- <el-row type="flex" :gutter="50">
            <el-col>
              <el-table :data="entityForm.indexlist" stripe :header-cell-style="headClass">
                <el-table-column label="Ad" align="center">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.cleanAd"></el-input>
                  </template>
                </el-table-column>
                <el-table-column label="S,td" align="center">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.cleanStd"></el-input>
                  </template>
                </el-table-column>
                <el-table-column label="Vdaf" align="center">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.cleanVdaf"></el-input>
                  </template>
                </el-table-column>
                <el-table-column label="G" align="center">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.rawG"></el-input>
                  </template>
                </el-table-column>
                <el-table-column label="Y" align="center">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.procY"></el-input>
                  </template>
                </el-table-column>
                <el-table-column label="X" align="center">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.procX"></el-input>
                  </template>
                </el-table-column>
                <el-table-column label="水分" align="center">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.cleanMt"></el-input>
                  </template>
                </el-table-column>
                <el-table-column label="反射率" align="center">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.macR0"></el-input>
                  </template>
                </el-table-column>
                <el-table-column label="标准差" align="center">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.macS"></el-input>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row> -->

          <el-col v-if="entityForm.indexlist">
            <EditTable ref="editTable2" v-model="entityForm.indexlist" :columns="getColumns" :rowHeaders="true"
                       @inited="handleEditTableInit">
            </EditTable>
          </el-col>

        </indicators-card>
        <indicators-card
          :subtitle="`(当前反射率之和为${entityForm.rate>0?entityForm.rate:'空'}，反射率要求在99.5到100.5之间)`"
          title="反射率分布表">
          <!-- <el-row>
            <el-col :span="6" v-for="item in entityForm.coalRockList" :key="item.sort">
              <el-form-item :label="item.rangeName">
                <el-input v-model="item.proportion" type="number" @input="changeRate">
                  <template slot="append">(%)</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row> -->
          <el-row :gutter="80" type="flex">
            <el-col>
              <EditTable ref="editTable1" v-model="editData1" :colHeaders="true" :columns="getColumns1"
                         @inited="handleEditTableInit1"></EditTable>
            </el-col>
          </el-row>
        </indicators-card>
        <!-- v-if="details.id" -->
        <indicators-card title="图表信息">
          <template v-if="entityForm && entityForm.coalTypeProportionList.length">
            <new-column-chart :impChartData="entityForm" isShowLabel style="width: 100%; height: 300px;"/>
            <el-table :data="entityForm.coalTypeProportionList" :show-header="false" border class="chart-table">
              <el-table-column prop="brownCoal"/>
              <el-table-column prop="longFlame"/>
              <el-table-column prop="gasCoal"/>
              <el-table-column prop="thirdCokingCoal"/>
              <el-table-column prop="fatCoal"/>
              <el-table-column prop="cokingCoal"/>
              <el-table-column prop="leanCoal"/>
              <el-table-column prop="meagerLeanCoal"/>
              <el-table-column prop="meagerCoal"/>
              <el-table-column prop="smokelessCoal"/>
            </el-table>
          </template>
        </indicators-card>
        <!-- v-if="details.id" -->
        <indicators-card title="备注">
          <el-form-item class="textarea" prop="remarks">
            <el-input v-model="entityForm.remarks" :rows="6" placeholder="请填写备注信息~" type="textarea">
            </el-input>
          </el-form-item>
        </indicators-card>
        <!-- v-if="details.id" -->
        <indicators-card title="附件信息">
          <el-row :gutter="80" type="flex">
            <el-col>
              <el-form-item label="">
                <el-collapse v-model="collapse">
                  <el-collapse-item name="1" title="上传附件">
                    <div class="upload">
                      <div class="upload-list">
                        <div v-for="(item,index) in uploadList" :key="index" class="up-load-warp">
                          <i class="el-icon-close icon" @click="handleRemoveUpload(index)"></i>
                          <el-link :href="item.uri" :underline=" false" class="up-load-warp-link">{{ item.display }}
                          </el-link>
                        </div>
                        <upload-attachment ref="upload" :limitNum="limitNum" :size="3"
                                           :uploadData="uploadData"
                                           accept=".xls,.xlsx,.pdf,.docx,.pdf,.png,.jpg,.jpeg" class="upload-attachment"
                                           listType="text"
                                           source="coalSample" @deleteNotify="handleUploadDelete"
                                           @successNotify="handleUploadSuccess"/>
                      </div>
                    </div>

                  </el-collapse-item>
                </el-collapse>

              </el-form-item>
            </el-col>
          </el-row>
        </indicators-card>

      </el-form>
      <!-- <el-dialog append-to-body title="煤种不一致" :visible.sync="isChangeCoal" width="80%" :before-close="handleClose">
        <div style="display: flex;font-size: 14px;">
          当前您选择的煤种是
          <span style="color: #A50D0F;font-size: 16px;">{{ entityForm.type }}</span>
          ,系统判断煤种是
          <span style="color: #A50D0F;font-size: 16px;">{{ type }}</span>
          ,是否修改煤种?
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button @click="isChangeCoal = false">取 消</el-button>
          <el-button class="dialog-footer-btns" @click="makeSure('t')" type="primary"
                     :loading="entityFormLoading">直接保存</el-button>
          <el-button class="dialog-footer-btns" style="width: 126px;" type="primary" :loading="entityFormLoadingV"
                     @click="makeSure(type)">确定修改并保存</el-button>
        </span>
      </el-dialog> -->
    </div>
    <div v-if="dialogStatus!=='watch'" slot="footer" class="dialog-footer">
      <el-button class="dialog-footer-btns" @click="handleCloseV">
        取消
      </el-button>
      <!-- <el-button class="dialog-footer-btns" type="primary" @click="save" style="width:60px">保存</el-button> -->

      <el-button :loading="entityFormLoading" class="dialog-footer-btns" style="width:60px" type="primary"
                 @click="makeSure('t')">保存
      </el-button>

    </div>
  </el-dialog>
</template>

<script>
import Func from '@/utils/func'
import {dateFormat} from '@/filters'
import NewColumnChart from '@/components/Chart/NewColumnChart'
import {IndicatorsCard, IndicatorsTable} from '@/components/Indicators/index'
import {getCoalById, coalAshList, isCheckCoal, saveCoal, getRangeListApi} from '@/api/coal'
import {deepClone} from '@/utils/index'
import EditTable from '@/components/EditTable/index.vue'

const entityForm = () => {
  return {
    id: '',
    userId: '',
    name: '',
    batchCode: dateFormat(new Date(), 'yyyymmdd'),
    type: '',
    province: '',
    factoryPrice: '',
    transitFee: '',
    roadCost: '',
    arrivePrice: '',
    arriveFactory: 'JT',
    mineDepth: '',
    rawMt: '',
    rawAd: '',
    rawPointFive: '',
    rawOnePointFour: '',
    rawAdIn: '',
    rawStd: '',
    rawVdaf: '',
    rawG: '',
    cleanVdaf: '',
    cleanVd: '',
    cleanAd: '',
    cleanStd: '',
    cleanMt: '',
    cleanP: '',
    cleanMci: '',
    procG: '',
    procY: '',
    procX: '',
    procMf: '',
    procTp: '',
    procTmax: '',
    procTk: '',
    procCrc: '',
    procA: '',
    procB: '',
    macR0: '',
    macS: '',
    macV: '',
    macI: '',
    macE: '',
    comSiO2: '',
    comAl2O3: '',
    comFe2O3: '',
    comCaO: '',
    comMgO: '',
    comNa2O: '',
    comK2O: '',
    comTiO2: '',
    comP2O5: '',
    comSO3: '',
    cfeCp: '',
    cfeCe: '',
    qualScon: '',
    qualPcon: '',
    qualM40: '',
    qualM10: '',
    qualCsr: '',
    qualCri: '',
    qualTestCond: '',
    crGk: '',
    crBk: '',
    crTk: '',
    city: '',
    dataType: '',
    createBy: '',
    createDate: '',
    updateBy: '',
    updateDate: '',
    remarks: '',
    ext: '',
    location: '',
    area: '',
    longitude: '',
    latitude: '',
    isFavorite: '-',
    rawPrice: '',
    coalTypeProportionList: []
  }
}
export default {
  name: 'coal',
  components: {
    EditTable,
    IndicatorsCard,
    IndicatorsTable,
    NewColumnChart
  },
  data() {
    return {
      entityForm: entityForm(),
      entityFormLoading: false,
      entityFormLoadingV: false,
      isChangeCoal: false,
      type: '',
      visible: false,
      editData1: [
        ...Array(10)
          .fill(null)
          .map(() => ({}))
      ],
      collapse: '1',
      limitNum: 1000,
      uploadList: [], // 用于展示
      uploadData: {refId: '', refType: 'CoalSample'}
    }
  },
  computed: {
    region: {
      set(val) {
        this.entityForm = Object.assign(this.entityForm, {
          province: val[0],
          city: val[1],
          area: val[2]
        })
      },
      get() {
        return [this.entityForm.province, this.entityForm.city, this.entityForm.area]
      }
    },
    // size() {
    //     return this.uploadList.length
    // },
    getColumnsInfo() {
      const dialogStatus = this.dialogStatus
      const len = this.entityForm.Basicinformation.length
      const watchArr =
        dialogStatus === 'watch'
          ? [
            {
              data: null,
              title: '',
              width: 30,
              //只读
              readOnly: true,
              renderer: function (instance, td, row, col, prop, value, cellProperties) {
                Handsontable.renderers.TextRenderer.apply(this, arguments)
                const index = row + 1
                td.style.textAlign = 'center'
                // td.innerHTML = index == len ? '平均' : index
              }
            }
          ]
          : []
      return [
        ...watchArr,
        {
          title: '名称',
          width: 60,
          data: 'name',
          type: 'text'
        },

        {
          title: '煤种',
          width: 100,
          data: 'type',
          formRequire: true, // 自定义字段
          visibleRows: 15,
          type: 'dropdown',
          source: (window.dict['coal_type'] || []).map((item) => item.code)
        },

        {
          title: '省市区',
          width: 100,
          data: 'location'
          // formRequire: true, // 自定义字段
          // visibleRows: 15,
          // type: 'dropdown',
          // source: (window.dict['coal_type'] || []).map((item) => item.code)
        },
        {
          title: '批次号',
          width: 60,
          data: 'batchCode',
          type: 'numeric',
          numericFormat: {
            pattern: '0.00'
          }
        },
        {
          title: '到厂价',
          width: 60,
          data: 'arrivePrice',
          type: 'numeric',
          numericFormat: {
            pattern: '0.00'
          }
        }
      ]
    },

    getColumns() {
      const dialogStatus = this.dialogStatus
      const len = this.entityForm.indexlist.length
      const watchArr =
        dialogStatus === 'watch'
          ? [
            {
              data: null,
              title: '',
              width: 30,
              //只读
              readOnly: true,
              renderer: function (instance, td, row, col, prop, value, cellProperties) {
                Handsontable.renderers.TextRenderer.apply(this, arguments)
                const index = row + 1
                td.style.textAlign = 'center'
                td.innerHTML = index == len ? '平均' : index
              }
            }
          ]
          : []
      return [
        ...watchArr,
        {
          title: 'Ad',
          width: 60,
          data: 'cleanAd',
          type: 'numeric',
          numericFormat: {
            pattern: '0.00'
          }
        },
        {
          title: 'St,d',
          width: 60,
          data: 'cleanStd',
          type: 'numeric',
          numericFormat: {
            pattern: '0.00'
          }
        },

        {
          title: 'Vdaf',
          width: 60,
          data: 'cleanVdaf',
          type: 'numeric',
          numericFormat: {
            pattern: '0.00'
          }
        },

        {
          title: '焦渣',
          width: 60,
          data: 'procCrc',
          type: 'numeric',
          numericFormat: {
            pattern: '0'
          }
        },
        {
          title: 'G',
          width: 60,
          data: 'procG',
          type: 'numeric',
          numericFormat: {
            pattern: '0'
          }
        },
        {
          title: 'Y',
          width: 60,
          data: 'procY',
          type: 'numeric',
          numericFormat: {
            pattern: '0.0'
          }
        },
        {
          title: 'X',
          width: 60,
          data: 'procX',
          type: 'numeric',
          numericFormat: {
            pattern: '0.0'
          }
        },
        {
          title: 'Mt',
          width: 60,
          data: 'cleanMt',
          type: 'numeric',
          numericFormat: {
            pattern: '0.0'
          }
        },
        {
          title: 'CSR',
          width: 60,
          data: 'csr',
          type: 'numeric',
          numericFormat: {
            pattern: '0.0'
          }
        },
        {
          title: '反射率',
          width: 60,
          data: 'macR0',
          type: 'numeric',
          numericFormat: {
            pattern: '0.000'
          }
        },
        {
          title: '标准差',
          width: 60,
          data: 'macS',
          type: 'numeric',
          numericFormat: {
            pattern: '0.000'
          }
        }
      ]
    },

    getColumns1() {
      const createNames = (index) => {
        const res = {
          0: 'Rran范围',
          1: '频率%',
          2: 'Rran范围',
          3: '频率%',
          4: 'Rran范围',
          5: '频率%',
          6: 'Rran范围',
          7: '频率%',
          8: 'Rran范围',
          9: '频率%'
        }
        return res[index] || 'default'
      }
      return [
        ...Array(10)
          .fill(null)
          .map((val, index, arr) => {
            // index 是偶数
            const dataProp = index + 1
            if (index % 2 == 1) {
              return {
                title: createNames(index),
                width: 80,
                data: dataProp,
                type: 'numeric',
                numericFormat: {
                  pattern: '0.00'
                }
              }
            }
            return {
              title: createNames(index),
              width: 90,
              data: dataProp,
              // 禁用列
              readOnly: true
            }
          })
      ]
    }
  },
  props: {
    details: {
      type: Object,
      default() {
        return {}
      }
    },
    addVisible: {
      type: Boolean,
      default: false
    },
    isSourceWash: {
      type: Boolean,
      default: false
    },
    dialogStatus: {
      type: String,
      default: 'update'
    }
  },
  methods: {
    handleCloseV() {
      this.addVisible = false
    },
    async handleRemoveUpload(index) {
      const {id} = this.uploadList[index]
      this.$refs.upload.attachmentDelete({id, index})
    },
    handleUploadSuccess(res) {
      this.uploadList = [...this.uploadList, res]
      // 上传完毕后更新展示的列表和把新增的附件id塞入到
      this.entityForm.attachmentList.push({id: res.id})
    },
    handleUploadDelete({status, index}) {
      this.uploadList.splice(index, 1)
    },

    //  基本信息表格
    async handleEditTableInitInfo(instance) {
      const getSetting = () => {
        if (this.dialogStatus === 'watch') {
          return {
            readOnly: true,
            contextMenu: undefined,
            data: this.entityForm.Basicinformation,
            rowHeaders: false
          }
        }
        return {
          contextMenu: {
            items: {}
          },
          data: this.entityForm.Basicinformation
        }
      }
      instance.updateSettings({
        columns: this.getColumnsInfo,
        ...getSetting(),
        async cells(row, col) {
        }
      })
    },
    // 指标表格
    async handleEditTableInit(instance) {
      const getSetting = () => {
        if (this.dialogStatus === 'watch') {
          return {
            readOnly: true,
            // height: '350',
            contextMenu: undefined,
            data: this.entityForm.indexlist,
            rowHeaders: false
          }
        }
        return {
          contextMenu: {
            items: {
              // row_above: { name: '向上插入一行' },
              // row_below: { name: '向下插入一行' },
              // remove_row: { name: '删除行' },
              // clear_custom: {
              //   name: '清空所有单元格数据',
              //   callback() {
              //     this.clear()
              //   }
              // }
            }
          },
          data: this.entityForm.indexlist
        }
      }
      instance.updateSettings({
        columns: this.getColumns,
        ...getSetting(),
        async cells(row, col) {
        }
      })
    },

    // 反射率表格
    async handleEditTableInit1(instance) {
      const getSetting = () => {
        if (this.dialogStatus === 'watch') {
          return {
            readOnly: true,
            contextMenu: false,
            data: this.editData1
          }
        }
        return {
          contextMenu: false,
          data: this.editData1,
          afterChange: (changes, source) => {
            changes?.forEach(([row, prop, oldValue, newValue]) => {
              var total = 0
              this.editData1.forEach((value, index) => {
                if (index !== 0) {
                  for (var i in value) {
                    if (i % 2 == 0) {
                      total += value[i]
                    }
                  }
                }
              })
              // changes.forEach((value) => {
              //   total += value[3]
              // })
              // Some logic...
              // if (row !== 0) {
              //   console.log(prop)
              //   this.$refs.editTable1.getHotInstance().setDataAtCell(0, 7, total)
              // }
            })
          }
        }
      }
      instance.updateSettings({
        ...getSetting(),
        async cells(row, col) {
          if (row === 0) {
            this.numericFormat = {
              pattern: '0.00'
            }
            // if (col !== 7) {
            //   this.numericFormat = {
            //     pattern: '0.000'
            //   }
            // } else {
            //   this.numericFormat = {
            //     pattern: '0.00'
            //   }
            // }
          }
        }
      })
    },

    headClass() {
      return 'text-align:center;background:#2f79e8;color:#fff;font-weight:400;border:none;'
    },
    async getRangerList() {
      const res = await Func.fetch(getRangeListApi)
      let coalRockList = []
      res.data.forEach((item) => {
        let range = {
          rangeName: '',
          proportion: '',
          sort: '',
          begin: '',
          end: ''
        }
        range.rangeName = item.rangeName
        range.sort = item.sort
        range.proportion = item.proportion
        range.begin = item.begin
        range.end = item.end
        coalRockList.push(range)
      })

      let indexlist = [
        ...Array(1)
          .fill(null)
          .map(() => ({}))
      ]
      let Basicinformation = [
        ...Array(1)
          .fill(null)
          .map(() => ({}))
      ]

      await this.getCreateData({
        list: coalRockList
      })

      this.entityForm = {...entityForm(), coalRockList, indexlist, Basicinformation}
    },
    // 保存数据
    async save() {
      const valid = await this.$refs.entityForm.validate()
      let that = this
      if (valid) {
        this.entityFormLoading = true
        const status = await this.$refs['editTable'].validate()
        if (!status) return
        const formatEditData1 = () => {
          const [first, ...rest] = this.getFormatEditData(this.editData1, this.getColumns1, false)
          let list = []
          rest.forEach((item) => {
            list.push({rangeName: item[1], proportion: item[2]})
            list.push({rangeName: item[3], proportion: item[4]})
            list.push({rangeName: item[5], proportion: item[6]})
            list.push({rangeName: item[7], proportion: item[8]})
            list.push({rangeName: item[9], proportion: item[10]})
          })
          list = list.filter((item) => item.rangeName)
          console.log(list, 'list')
          return {
            coalRockList: list
          }
        }
        formatEditData1()
        this.entityForm.Basicinformation.forEach((item) => {
          this.entityForm.name = item.name
          this.entityForm.type = item.type
          this.entityForm.location = item.location
          this.entityForm.batchCode = item.batchCode
          this.entityForm.arrivePrice = item.arrivePrice
        })

        this.entityForm.indexlist.forEach((item) => {
          this.entityForm.cleanAd = item.cleanAd
          this.entityForm.cleanStd = item.cleanStd
          this.entityForm.cleanVdaf = item.cleanVdaf
          this.entityForm.procCrc = item.procCrc
          this.entityForm.rawG = item.rawG
          this.entityForm.procY = item.procY
          this.entityForm.procX = item.procX
          this.entityForm.cleanMt = item.cleanMt
          this.entityForm.macR0 = item.macR0
          this.entityForm.macS = item.macS
        })

        const res = await Func.fetch(isCheckCoal, {
          g: this.entityForm.procG,
          vdaf: this.entityForm.cleanVdaf,
          y: this.entityForm.procY
        })
        if (res.data) {
          if (res.data.name !== this.entityForm.type) {
            that.isChangeCoal = true
            that.type = res.data.name
            that.entityFormLoading = false
          } else {
            that.entityFormLoading = true
            //  基础信息
            let ting = that.entityForm.Basicinformation
            for (var i = 0; i < ting.length; i++) {
              that.entityForm.name = ting[i].name
              that.entityForm.type = ting[i].type
              that.entityForm.location = ting[i].location
              that.entityForm.batchCode = ting[i].batchCode
              that.entityForm.arrivePrice = ting[i].arrivePrice
            }
            // 指标信息
            let Things = this.entityForm.indexlist
            for (var i = 0; i < Things.length; i++) {
              that.entityForm.cleanAd = Things[i].cleanAd
              that.entityForm.cleanStd = Things[i].cleanStd
              that.entityForm.cleanVdaf = Things[i].cleanVdaf
              that.entityForm.rawG = Things[i].rawG
              that.entityForm.procY = Things[i].procY
              that.entityForm.procX = Things[i].procX
              that.entityForm.cleanMt = Things[i].cleanMt
              that.entityForm.macR0 = Things[i].macR0
              that.entityForm.macS = Things[i].macS
              that.entityForm.procCrc = Things[i].procCrc
            }
            const entityFormData = {
              ...that.entityForm,
              dataType: that.datatype,
              attachmentList: that.uploadList,
              ...formatEditData1()
            }
            const saveRes = await Func.fetch(saveCoal, entityFormData)

            this.entityFormLoading = false
            if (saveRes.data) {
              this.$message({showClose: true, message: '提交成功', type: 'success'})
              this.closeDialog('full', true)
              this.handleClose('full')
            }
          }
        }
      }
    },
    /**
     * 过滤掉空数据，校验数据
     * @param target 提供的车牌列表
     * @param isSubmit 是否提交 主要用于控制是否校验车牌号 true时抛出Error
     */
    getFormatEditData(target = [], columns, require = true) {
      const getExcludeDirtyList = (target = []) => {
        const list = deepClone(target)
        list.forEach((v) => {
          for (const key in v) {
            if ([null, ''].includes(v[key])) {
              delete v[key]
            }
          }
        })
        return list
      }
      const list = getExcludeDirtyList(target)
      const data = []
      // 获取需要校验的字段
      const validateList = columns
        .filter((col) => col.formRequire)
        .map((v) => {
          return {key: v.data, msg: v.title}
        })

      list.forEach((item, index) => {
        // 过滤掉空数据 但在编辑的时候会保留id所以不能被删除只能修改当前这条数据
        if (item && Object.keys(item).length) {
          validateList.forEach((v) => {
            if (!item[v.key]) {
              this.$message(`表格第${index + 1}行${v.msg}不能为空`)
              throw new Error('数据不能为空')
            }
          })
          data.push(item)
        }
      })

      if (!data.length && require) {
        this.$message({message: '表格至少添加一条数据', type: 'warning'})
        throw new Error('数据不能为空')
      }

      return data
    },

    async makeSure(coalType) {
      const {arriveFactory} = this.$refs.entityForm
      if (coalType === 't') {
        this.entityFormLoading = true
        this.entityForm.type = coalType || this.entityForm.type
      } else {
        this.entityFormLoadingV = true
        this.entityForm.type = ''
      }
      const formatEditData1 = () => {
        const [...rest] = this.getFormatEditData(this.editData1, this.getColumns1, false)
        let list = []
        rest.forEach((item) => {
          list.push({rangeName: item[1], proportion: item[2]})
          list.push({rangeName: item[3], proportion: item[4]})
          list.push({rangeName: item[5], proportion: item[6]})
          list.push({rangeName: item[7], proportion: item[8]})
          list.push({rangeName: item[9], proportion: item[10]})
        })
        list = list.filter((item) => item.rangeName)
        // console.log(list, 'list')
        return {
          coalRockList: list
        }
      }

      formatEditData1()
      //  基础信息
      let ting = this.$refs.editTable.getHotInstance().getSourceData()
      for (var i = 0; i < ting.length; i++) {
        this.entityForm.name = ting[i].name
        this.entityForm.type = ting[i].type
        this.entityForm.location = ting[i].location
        this.entityForm.batchCode = ting[i].batchCode
        this.entityForm.arrivePrice = ting[i].arrivePrice
      }
      // 指标信息
      let Things = this.$refs.editTable2.getHotInstance().getSourceData()
      for (var i = 0; i < Things.length; i++) {
        this.entityForm.cleanAd = Things[i].cleanAd
        this.entityForm.cleanStd = Things[i].cleanStd
        this.entityForm.cleanVdaf = Things[i].cleanVdaf
        this.entityForm.procG = Things[i].procG
        this.entityForm.procY = Things[i].procY
        this.entityForm.procX = Things[i].procX
        this.entityForm.cleanMt = Things[i].cleanMt
        this.entityForm.macR0 = Things[i].macR0
        this.entityForm.macS = Things[i].macS
        this.entityForm.csr = Things[i].csr
        this.entityForm.procCrc = Things[i].procCrc
      }

      const entityFormData = {
        ...this.entityForm,
        dataType: this.datatype,
        attachmentList: this.uploadList,
        arriveFactory,
        ...formatEditData1()
      }
      // const entityFormData = { ...this.entityForm, dataType: this.datatype, arriveFactory }
      const saveRes = await Func.fetch(saveCoal, entityFormData)
      if (coalType === 't') {
        this.entityFormLoading = false
      } else {
        this.entityFormLoadingV = false
      }

      if (saveRes.data) {
        this.$message({showClose: true, message: '提交成功', type: 'success'})
        // this.$router.back()
        // this.getEntityForm()
        this.closeDialog('full', true)
      }
    },
    closeDialog(type = false) {
      this.dialogFormVisible = false
      this.chartVisible = false
      this.entityForm = {...entityForm()}
      this.isChangeCoal = false
      this.handleClose('full', type)
      // this.$refs.entityForm.$children[0].resetFields()
    },
    async getEntityForm() {
      await this.$nextTick()
      const {id} = this.details
      try {
        const res = await getCoalById({id})
        if (res.data) {
          let rate = 0
          res.data.coalRockList.map((item) => {
            if (item.proportion) {
              rate = Number(item.proportion) + rate
            }
          })
          rate = rate.toFixed(1)
          let Basicinformation = []
          let obj = {
            name: res.data.name,
            type: res.data.type,
            arrivePrice: res.data.arrivePrice,
            location: res.data.location,
            batchCode: res.data.batchCode
          }
          Basicinformation.push(obj)
          res.data.Basicinformation = Basicinformation

          let indexlist = []
          let objv = {
            cleanAd: res.data.cleanAd,
            cleanStd: res.data.cleanStd,
            cleanVdaf: res.data.cleanVdaf,
            procG: res.data.procG,
            procCrc: res.data.procCrc,
            procY: res.data.procY,
            procX: res.data.procX,
            cleanMt: res.data.cleanMt,
            macR0: res.data.macR0,
            csr: res.data.csr,
            macS: res.data.macS
          }
          indexlist.push(objv)
          res.data.indexlist = indexlist
          this.entityForm = {...res.data, rate}
          this.uploadList = res.data.attachmentList
          await this.getCreateData({
            list: res.data.coalRockList
          })
          this.$forceUpdate()
          this.$nextTick(() => {
            this.$refs.editTable.getHotInstance().loadData([...Basicinformation])
            this.$refs.editTable2.getHotInstance().loadData([...indexlist])
          })
        }
        // console.log(this.entityForm, id, 111)
      } catch (error) {
      }
    },

    async getCreateData(obj = {}) {
      // console.log(obj)
      try {
        let data = []
        const result = [
          // {
          //   1: 'Rran范围',
          //   2: '频率%',
          //   3: 'Rran范围',
          //   4: '频率%',
          //   5: 'Rran范围',
          //   6: '频率%',
          //   7: 'Rran范围',
          //   8: '频率%',
          //   9: 'Rran范围',
          //   10: '频率%'
          // }
        ]
        if (obj.list) {
          data = obj.list
        } else {
          const res = await Func.fetch(getRangeListApi)
          data = res.data
        }

        function chunkArray(arr, chunkSize) {
          const chunkedArray = []
          for (let i = 0; i < arr.length; i += chunkSize) {
            chunkedArray.push(arr.slice(i, i + chunkSize))
          }
          return chunkedArray
        }

        chunkArray(data, 5).forEach((list) => {
          const obj = {}
          let i = 1
          list.forEach((v) => {
            obj[i] = v.rangeName
            obj[i + 1] = v.proportion
            i += 2
          })
          result.push(obj)
        })

        this.editData1 = [...result]
        this.$refs.editTable1.getHotInstance().loadData([...this.editData1])
      } catch (e) {
        console.log(e, 'e')
      }
    },

    handleActive(e) {
      // this.entityForm.activeInertiaRatio = (e / (100 - e)).toFixed(2)
      this.entityForm = {...this.entityForm, activeInertiaRatio: (e / (100 - e)).toFixed(2)}
    },
    handleVdaf(e) {
      if (this.entityForm.cleanAd) {
        this.entityForm = {...this.entityForm, cleanVd: ((e * (100 - this.entityForm.cleanAd)) / 100).toFixed(2)}
        // this.entityForm.cleanVd = ((e * (100 - this.entityForm.cleanAd)) / 100).toFixed(2)
      } else {
        this.entityForm = {...this.entityForm, cleanVd: 0}
        // this.entityForm.cleanVd = 0
      }
    },
    handleAd(e) {
      if (this.entityForm.cleanVdaf) {
        // this.entityForm.cleanVd = ((this.entityForm.cleanVdaf * (100 - e)) / 100).toFixed(2)
        this.entityForm = {...this.entityForm, cleanVd: ((this.entityForm.cleanVdaf * (100 - e)) / 100).toFixed(2)}
      } else {
        // this.entityForm.cleanVd = this.entityForm.cleanVdaf
        this.entityForm = {...this.entityForm, cleanVd: 0}
      }
    },
    handleVd(e) {
      if (this.entityForm.cleanAd) {
        // this.entityForm.cleanVdaf = ((100 * e) / (100 - this.entityForm.cleanAd)).toFixed(2)
        this.entityForm = {...this.entityForm, cleanVdaf: ((100 * e) / (100 - this.entityForm.cleanAd)).toFixed(2)}
      } else {
        // this.entityForm.cleanVdaf = e
        this.entityForm = {...this.entityForm, cleanVdaf: 0}
      }
    },
    changeRate() {
      this.entityForm.rate = 0
      this.entityForm.coalRockList.map((item) => {
        if (item.proportion) {
          this.entityForm.rate = Number(item.proportion) + this.entityForm.rate
        }
      })
    },
    /*
     * 数值范围验证器
     * */
    RangerValidate(rules, value, cb) {
      if (+value < 0) {
        cb(new Error('数值不能小于0'))
      } else if (+value > 100) {
        cb(new Error('数值不能大于100'))
      } else {
        cb()
      }
    },
    handleClose(type = '', isSave = false) {
      if (type === 'full') {
        this.dialogFormVisible = false
        this.chartVisible = false
        this.entityForm = {...entityForm()}
        this.uploadList = []
        this.isChangeCoal = false
        this.$emit('closeVisible', {isSave: isSave})
      } else {
        this.dialogVisible = false
        this.position = {}
        this.isChangeCoal = false
      }
    },
    handleSubmitPosition() {
      if (Object.keys(this.position).length === 0) {
        return
      }
      this.$set(this.entityForm, 'longitude', this.position.lng)
      this.$set(this.entityForm, 'latitude', this.position.lat)
      this.handleClose()
    },
    /**
     * 获取煤灰成份
     */

    async handleCoalAsh() {
      const location = {filter_EQS_area: this.entityForm.area}
      const res = await Func.fetch(coalAshList, location)
      if (res.data.records.length > 0) {
        delete res.data.records[0].id
        // this.$emit('update:entityForm', { ...this.entityForm, ...res.data.records[0] })
        this.entityForm = {...this.entityForm, ...res.data.records[0]}
      } else {
        this.$message({
          showClose: true,
          message: '无灰成分参考数据，请化验',
          type: 'error'
        })
      }
    },
    /**
     * 关闭煤灰成分弹框
     * @param done
     */
    handleCoalClose(done) {
      done()
    }
  },
  watch: {
    addVisible(v) {
      this.visible = v
    },
    'details.id'(v) {
      if (v) {
        console.log(this.dialogStatus)
        this.getEntityForm()
      } else {
        if (this.dialogStatus === 'create') {
          this.getRangerList()
        }
      }
    },

    details: {
      handler(value) {
        // console.log(value, 'v')
      },
      deep: true,
      immediate: true
    }
  }
}
</script>

<style lang="scss" scoped scoped>
@import '@/styles/router-page.scss';

::v-deep .el-collapse-item__arrow {
  display: none;
}

.upload {
  display: flex;
  align-content: center;
  justify-content: space-around;

  .upload-list {
    flex: 1;
    display: flex;
    align-content: center;
    justify-content: flex-start;

    .up-load-warp {
      display: flex;
      flex-flow: column;
      align-content: center;
      justify-content: center;
      padding: 6px 10px;
      margin-right: 5px;
      font-size: 12px;
      position: relative;

      &-link {
        font-size: 12px;
        opacity: 0.8;
      }

      .icon {
        cursor: pointer;
        position: absolute;
        right: 0;
        z-index: 9;
        top: 0;
        flex: 0;
      }
    }
  }
}

.add {
  ::v-deep .el-dialog__body {
    padding: 0 35px;
    height: 80vh;
  }
}

::v-deep .el-form-item__label {
  font-weight: 400;
}

::v-deep .indicators .title {
  display: block;
}

.textarea {
  ::v-deep .el-form-item__content {
    margin-left: 0 !important;
  }
}

::v-deep .el-form-item__content {
  margin-left: 0 !important;
}

.title-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.title_ad {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.el-col-6 {
  display: flex;
  justify-content: flex-start;
}

.saveBtn {
  display: flex;
  justify-content: flex-end;
}

.dictSelected {
  max-width: 100%;

  ::v-deep .el-input__inner {
    max-width: 100%;
  }
}

.chart-table {
  width: 80%;
  margin: 0 auto;
  margin-bottom: 10px;
  // display: flex;
  // justify-content: center;
}
</style>
