<template>
  <el-dialog class="add" :title="details.name || '新增'" width="90%" top="3vh"
             :visible.sync="visible"
             :append-to-body="isSourceWash" :before-close="()=>handleClose('full')">
    <div class="coal">
      <el-form ref="entityForm" :model="entityForm" label-width="80px">
        <indicators-card>
          <template #title>
            <div class="title-content">
              <span>基础信息</span>
              <!--                            <el-button type="primary" >保存-->
              <!--                            </el-button>-->
            </div>
          </template>
          <el-row>
            <el-col :span="6">
              <el-form-item label="名称" prop="name">
                <el-input v-model="entityForm.name" :rules="[{ required: true, message: '请输入名称' }]"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="煤种" prop="type" :rules="[{ required: true, message: '请选择一项' }]">
                <dict-select style="width:100%" v-model="entityForm.type" type="coal_type"></dict-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="省市区" prop="province" :rules="[{ required: true, message: '请输入省市' }]">
                <RegionSelectOld :value="region"
                                 @input="value=>((entityForm.location=value.location)||1)&&(region=value.val)">
                </RegionSelectOld>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item prop="batchCode" label="批次号" :rules="[{ required: true, message: '请输入批次号' }]">
                <el-input :value="entityForm.batchCode" type="number"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </indicators-card>
        <indicators-card title="原煤指标">
          <el-row>
            <el-col :span="6">
              <el-form-item label="全水" prop="rawMt" :rules="[{validator: this.RangerValidate}]">
                <el-input v-model="entityForm.rawMt" type="number">
                  <template slot="append">(%)</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="全灰" prop="rawAd" :rules="[{validator: this.RangerValidate}]">
                <el-input v-model="entityForm.rawAd" type="number">
                  <template slot="append">(%)</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="-0.5" prop="rawPointFive" :rules="[{validator: this.RangerValidate}]">
                <el-input v-model="entityForm.rawPointFive" type="number">
                  <template slot="append">(%)</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="-1.4" prop="rawOnePointFour" :rules="[{validator: this.RangerValidate}]">
                <el-input v-model="entityForm.rawOnePointFour" type="number">
                  <template slot="append">(%)</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="6">
              <el-form-item label="内灰" prop="rawAdIn" :rules="[{validator: this.RangerValidate}]">
                <el-input v-model="entityForm.rawAdIn" type="number">
                  <template slot="append">(%)</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="硫" prop="rawStd" :rules="[{validator: this.RangerValidate}]">
                <el-input v-model="entityForm.rawStd" type="number">
                  <template slot="append">(%)</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="挥发分" prop="rawVdaf">
                <el-input v-model="entityForm.rawVdaf" type="number">
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="粘结" prop="rawG" :rules="[{validator: this.RangerValidate}]">
                <el-input v-model="entityForm.rawG" type="number">
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="6">
              <el-form-item prop="rawPrice" label="价格">
                <el-input v-model="entityForm.rawPrice" type="number">
                  <template slot="append">(元/吨)</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </indicators-card>
        <indicators-card title="精煤指标">
          <el-row>
            <el-col :span="6">
              <el-form-item prop="cleanVdaf" label="Vdaf"
                            :rules="[{required:true, message:'请输入数值'},{validator: this.RangerValidate}]">
                <el-input @input="handleVdaf" v-model="entityForm.cleanVdaf" type="number">
                  <template slot="append">(%)</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item prop="cleanAd" label="Ad"
                            :rules="[{required:true, message:'请输入数值'},{validator: this.RangerValidate}]">
                <el-input v-model="entityForm.cleanAd" type="number" @input="handleAd">
                  <template slot="append">(%)</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item prop="cleanStd" label="St,d"
                            :rules="[{required:true, message:'请输入数值'},{validator: this.RangerValidate}]">
                <el-input v-model="entityForm.cleanStd" type="number">
                  <template slot="append">(%)</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item prop="cleanMt" label="Mt" :rules="[{validator: this.RangerValidate}]">
                <el-input v-model="entityForm.cleanMt" type="number">
                  <template slot="append">(%)</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="6">
              <el-form-item prop="cleanP" label="P" :rules="[{validator: this.RangerValidate}]">
                <el-input v-model="entityForm.cleanP" type="number">
                  <template slot="append">(%)</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item prop="cleanVd" label="Vd">
                <el-input v-model="entityForm.cleanVd" type="number" @input="handleVd">
                  <template slot="append">(%)</template>
                </el-input>
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item prop="arrivePrice" label="到厂价" :rules="[{required:true, message:'请输入到厂价'}]">
                <el-input v-model="entityForm.arrivePrice" type="number">
                  <template slot="append">(元/吨)</template>
                </el-input>
              </el-form-item>
            </el-col>

          </el-row>
        </indicators-card>
        <indicators-card title="工艺性能">
          <el-row>
            <el-col :span="6">
              <el-form-item prop="procG" label="G" :rules="[{required:true, message:'请输入数值'}]">
                <el-input v-model="entityForm.procG" type="number">
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item prop="procY" label="Y">
                <el-input v-model="entityForm.procY" type="number">
                  <template slot="append">(mm)</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item prop="procX" label="X">
                <el-input v-model="entityForm.procX" type="number">
                  <template slot="append">(mm)</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item prop="procMf" label="MF">
                <el-input v-model="entityForm.procMf" type="number">
                  <template slot="append">(ddpm)</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="6">
              <el-form-item prop="procTp" label="Tp">
                <el-input v-model="entityForm.procTp" type="number">
                  <template slot="append">(℃)</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item prop="procTmax" label="Tmax">
                <el-input v-model="entityForm.procTmax" type="number">
                  <template slot="append">(℃)</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item prop="procTk" label="Tk">
                <el-input v-model="entityForm.procTk" type="number">
                  <template slot="append">(℃)</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item prop="procCrc" label="特征">
                <el-input v-model="entityForm.procCrc" type="number">
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="6">
              <el-form-item label="a" prop="procA" :rules="[{validator: this.RangerValidate}]">
                <el-input v-model="entityForm.procA" type="number">
                  <template slot="append">(%)</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="b" prop="procB">
                <el-input v-model="entityForm.procB" type="number">
                  <template slot="append">(%)</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </indicators-card>
        <indicators-card title="煤岩组分">
          <el-row>
            <el-col :span="6">
              <el-form-item prop="macR0" label="反射率(R0)">
                <el-input v-model="entityForm.macR0" type="number">
                  <template slot="append">(%)</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item prop="macS" label="标准差(S)">
                <el-input v-model="entityForm.macS" type="number">
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item prop="macV" label="镜质组(V)" :rules="[{validator: this.RangerValidate}]">
                <el-input v-model="entityForm.macV" type="number">
                  <template slot="append">(%)</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item prop="macI" label="丝质组(I)" :rules="[{validator: this.RangerValidate}]">
                <el-input v-model="entityForm.macI" type="number">
                  <template slot="append">(%)</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="6">
              <el-form-item prop="macE" label="稳定组(E)" :rules="[{validator: this.RangerValidate}]">
                <el-input v-model="entityForm.macE" type="number">
                  <template slot="append">(%)</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item prop="macE" label="活性物">
                <el-input v-model="entityForm.active" type="number" @input="handleActive">
                  <template slot="append">(%)</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item prop="activeInertiaRatio" label="活惰比">
                <el-input v-model="entityForm.activeInertiaRatio" type="number">
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </indicators-card>
        <indicators-card title="煤灰成分">
          <template #title>
            <div class="title_ad">
              <span>煤灰成分</span>
              <el-button style="border-radius:4px" type="primary" size="mini" @click="handleCoalAsh">
                获取灰成分参考值
              </el-button>
            </div>
          </template>
          <el-row>
            <el-col :span="6">
              <el-form-item prop="comSiO2">
                <span slot="label">SiO<sub>2</sub></span>
                <el-input v-model="entityForm.comSiO2" type="number">
                  <template slot="append">(%)</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item prop="comAl2O3">
                <span slot="label">Al<sub>2</sub>O<sub>3</sub></span>
                <el-input v-model="entityForm.comAl2O3" type="number">
                  <template slot="append">(%)</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="Fe2O3" prop="comFe2O3">
                <span slot="label">Fe<sub>2</sub>O<sub>3</sub></span>
                <el-input v-model="entityForm.comFe2O3" type="number">
                  <template slot="append">(%)</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="CaO" prop="comCaO">
                <el-input v-model="entityForm.comCaO" type="number">
                  <template slot="append">(%)</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="6">
              <el-form-item prop="comMgO" label="MgO">
                <el-input v-model="entityForm.comMgO" type="number">
                  <template slot="append">(%)</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item prop="comNa2O" label="Na2O">
                <span slot="label">Na<sub>2</sub>O</span>
                <el-input v-model="entityForm.comNa2O" type="number">
                  <template slot="append">(%)</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item prop="comK2O" label="K2O">
                <span slot="label">K<sub>2</sub>O</span>
                <el-input v-model="entityForm.comK2O" type="number">
                  <template slot="append">(%)</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item prop="comTiO2" label="TiO2">
                <span slot="label">TiO<sub>2</sub></span>
                <el-input v-model="entityForm.comTiO2" type="number">
                  <template slot="append">(%)</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="6">
              <el-form-item prop="comP2O5" label="P2O5">
                <span slot="label">P<sub>2</sub>O<sub>5</sub></span>
                <el-input v-model="entityForm.comP2O5" type="number">
                  <template slot="append">(%)</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item prop="comSO3" label="SO3">
                <span slot="label">SO<sub>3</sub></span>
                <el-input v-model="entityForm.comSO3" type="number">
                  <template slot="append">(%)</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </indicators-card>
        <indicators-card title="成煤环境">
          <el-row>
            <el-col :span="6">
              <el-form-item prop="cfeCp" label="成煤期">
                <dict-select v-model="entityForm.cfeCp" type="coal_form_period"></dict-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item prop="cfeCe" label="成煤环境">
                <dict-select v-model="entityForm.cfeCe" type="coal_form_env"></dict-select>
              </el-form-item>
            </el-col>
          </el-row>
        </indicators-card>
        <indicators-card title="质量指标">
          <el-row>
            <el-col :span="6">
              <el-form-item prop="qualScon" label="硫转换率" :rules="[{validator: this.RangerValidate}]">
                <el-input v-model="entityForm.qualScon" type="number"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item prop="qualM40" label="M40" :rules="[{validator: this.RangerValidate}]">
                <el-input v-model="entityForm.qualM40" type="number">
                  <template slot="append">(%)</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item prop="qualM10" label="M10" :rules="[{validator: this.RangerValidate}]">
                <el-input v-model="entityForm.qualM10" type="number">
                  <template slot="append">(%)</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item prop="qualCsr" label="CSR" :rules="[{validator: this.RangerValidate}]">
                <el-input v-model="entityForm.qualCsr" type="number">
                  <template slot="append">(%)</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="6">
              <el-form-item prop="qualCri" label="CRI" :rules="[{validator: this.RangerValidate}]">
                <el-input v-model="entityForm.qualCri" type="number">
                  <template slot="append">(%)</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item prop="qualTestCond" label="实验条件">
                <el-input v-model="entityForm.qualTestCond" type="number">
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </indicators-card>
        <indicators-card title="化产回收">
          <el-row>
            <el-col :span="6">
              <el-form-item prop="crGk" label="煤气产量">
                <el-input v-model="entityForm.crGk" type="number">
                  <template slot="append">(Nm<sup>3</sup>)</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item prop="crBk" label="粗苯产率" :rules="[{validator: this.RangerValidate}]">
                <el-input v-model="entityForm.crBk" type="number">
                  <template slot="append">(%)</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item prop="crTk" label="焦油产率" :rules="[{validator: this.RangerValidate}]">
                <el-input v-model="entityForm.crTk" type="number">
                  <template slot="append">(%)</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </indicators-card>
        <indicators-card title="精煤指标"
                         :subtitle="`(当前反射率之和为${entityForm.rate>0?entityForm.rate:'空'}，反射率要求在99.5到100.5之间)`">
          <el-row>
            <el-col :span="6" v-for="item in entityForm.coalRockList" :key="item.sort">
              <el-form-item :label="item.rangeName">
                <el-input v-model="item.proportion" type="number" @input="changeRate">
                  <template slot="append">(%)</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </indicators-card>
        <indicators-card v-if="details.id" title="图表信息">
          <template v-if="entityForm && entityForm.coalTypeProportionList.length">
            <new-column-chart :impChartData="entityForm" isShowLabel style="width: 100%; height: 300px;"/>
            <el-table :data="entityForm.coalTypeProportionList" border :show-header="false" class="chart-table">
              <el-table-column prop="brownCoal"/>
              <el-table-column prop="longFlame"/>
              <el-table-column prop="gasCoal"/>
              <el-table-column prop="thirdCokingCoal"/>
              <el-table-column prop="fatCoal"/>
              <el-table-column prop="cokingCoal"/>
              <el-table-column prop="leanCoal"/>
              <el-table-column prop="meagerLeanCoal"/>
              <el-table-column prop="meagerCoal"/>
              <el-table-column prop="smokelessCoal"/>
            </el-table>
          </template>
        </indicators-card>
        <indicators-card v-if="details.id" title="备注">
          <el-form-item prop="remarks" class="textarea">
            <el-input v-model="entityForm.remarks" type="textarea" :rows="6" placeholder="请填写备注信息~">
            </el-input>
          </el-form-item>
        </indicators-card>
      </el-form>
      <el-dialog append-to-body title="煤种不一致" :visible.sync="isChangeCoal" width="80%" :before-close="handleClose">
        <div style="display: flex;font-size: 14px;">
          当前您选择的煤种是
          <span style="color: #A50D0F;font-size: 16px;">{{ entityForm.type }}</span>
          ,系统判断煤种是
          <span style="color: #A50D0F;font-size: 16px;">{{ type }}</span>
          ,是否修改煤种?
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button @click="isChangeCoal = false">取 消</el-button>
          <el-button @click="makeSure('')" type="primary" :loading="entityFormLoading">直接保存</el-button>
          <el-button type="primary" @click="makeSure(type)" :loading="entityFormLoading">确定修改并保存</el-button>
        </span>
      </el-dialog>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button class="dialog-footer-btns" @click="save" style="width:60px">保存</el-button>
    </div>
  </el-dialog>

</template>

<script>
import Func from '@/utils/func'
import {dateFormat} from '@/filters'
import NewColumnChart from '@/components/Chart/NewColumnChart'
import {IndicatorsCard, IndicatorsTable} from '@/components/Indicators/index'
import {getCoalById, coalAshList, isCheckCoal, saveCoal, getRangeListApi} from '@/api/coal'

const entityForm = {
  id: '',
  userId: '',
  name: '',
  batchCode: dateFormat(new Date(), 'yyyymmdd'),
  type: '',
  province: '',
  factoryPrice: '',
  transitFee: '',
  roadCost: '',
  arrivePrice: '',
  arriveFactory: 'JT',
  mineDepth: '',
  rawMt: '',
  rawAd: '',
  rawPointFive: '',
  rawOnePointFour: '',
  rawAdIn: '',
  rawStd: '',
  rawVdaf: '',
  rawG: '',
  cleanVdaf: '',
  cleanAd: '',
  cleanStd: '',
  cleanMt: '',
  cleanP: '',
  cleanMci: '',
  procG: '',
  procY: '',
  procX: '',
  procMf: '',
  procTp: '',
  procTmax: '',
  procTk: '',
  procCrc: '',
  procA: '',
  procB: '',
  macR0: '',
  macS: '',
  macV: '',
  macI: '',
  macE: '',
  comSiO2: '',
  comAl2O3: '',
  comFe2O3: '',
  comCaO: '',
  comMgO: '',
  comNa2O: '',
  comK2O: '',
  comTiO2: '',
  comP2O5: '',
  comSO3: '',
  cfeCp: '',
  cfeCe: '',
  qualScon: '',
  qualPcon: '',
  qualM40: '',
  qualM10: '',
  qualCsr: '',
  qualCri: '',
  qualTestCond: '',
  crGk: '',
  crBk: '',
  crTk: '',
  city: '',
  dataType: '',
  createBy: '',
  createDate: '',
  updateBy: '',
  updateDate: '',
  remarks: '',
  ext: '',
  location: '',
  area: '',
  longitude: '',
  latitude: '',
  isFavorite: '-',
  rawPrice: '',
  coalTypeProportionList: []
}
export default {
  name: 'coal',
  components: {
    IndicatorsCard,
    IndicatorsTable,
    NewColumnChart
  },
  data() {
    return {
      entityForm: {...entityForm},
      entityFormLoading: false,
      isChangeCoal: false,
      type: '',
      visible: false
    }
  },
  computed: {
    region: {
      set(val) {
        this.entityForm = Object.assign(this.entityForm, {
          province: val[0],
          city: val[1],
          area: val[2]
        })
      },
      get() {
        return [this.entityForm.province, this.entityForm.city, this.entityForm.area]
      }
    }
  },
  props: {
    details: {
      type: Object,
      default() {
        return {}
      }
    },
    addVisible: {
      type: Boolean,
      default: false
    },
    isSourceWash: {
      type: Boolean,
      default: false
    }
  },
  created() {
    // if (this.details.id) {
    //     this.getEntityForm()
    // } else {
    //     this.getRangerList()
    // }
  },
  methods: {
    async getRangerList() {
      const res = await Func.fetch(getRangeListApi)
      let coalRockList = []
      res.data.forEach((item) => {
        let range = {
          rangeName: '',
          proportion: '',
          sort: '',
          begin: '',
          end: ''
        }
        range.rangeName = item.rangeName
        range.sort = item.sort
        range.proportion = item.proportion
        range.begin = item.begin
        range.end = item.end
        coalRockList.push(range)
      })
      this.entityForm = {...entityForm, coalRockList}
    },
    // 保存数据
    async save() {
      // const {arriveFactory} = this.$refs.entityForm
      // const entityForm = this.$refs.entityForm.$children[0]
      const valid = await this.$refs.entityForm.validate()
      if (valid) {
        const res = await Func.fetch(isCheckCoal, {
          g: this.entityForm.procG,
          vdaf: this.entityForm.cleanVdaf,
          y: this.entityForm.procY
        })
        if (res.data) {
          if (res.data.name !== this.entityForm.type) {
            this.isChangeCoal = true
            this.type = res.data.name
          } else {
            this.entityFormLoading = true
            const entityFormData = {...this.entityForm, dataType: this.datatype}
            const saveRes = await Func.fetch(saveCoal, entityFormData)
            this.entityFormLoading = false
            if (saveRes.data) {
              this.$message({showClose: true, message: '提交成功', type: 'success'})
              this.closeDialog('full', true)
              // this.handleClose('full')
            }
          }
        }
      }
    },
    async makeSure(coalType) {
      const {arriveFactory} = this.$refs.entityForm
      this.entityForm.type = coalType || this.entityForm.type
      this.entityFormLoading = true
      const entityFormData = {...this.entityForm, dataType: this.datatype, arriveFactory}
      const saveRes = await Func.fetch(saveCoal, entityFormData)
      this.entityFormLoading = false
      if (saveRes.data) {
        this.$message({showClose: true, message: '提交成功', type: 'success'})
        // this.$router.back()
        // this.getEntityForm()
        this.closeDialog('full', true)
      }
    },
    closeDialog(type = '', isSave = false) {
      this.dialogFormVisible = false
      this.chartVisible = false
      this.entityForm = {...entityForm}
      this.isChangeCoal = false
      this.$emit('closeVisible', {isSave: isSave})
      // this.$refs.entityForm.$children[0].resetFields()
    },
    async getEntityForm() {
      await this.$nextTick()
      const {id} = this.details
      try {
        const res = await getCoalById({id})
        if (res.data) {
          let rate = 0
          res.data.coalRockList.map((item) => {
            if (item.proportion) {
              rate = Number(item.proportion) + rate
            }
          })
          rate = rate.toFixed(1)
          this.entityForm = {...res.data, rate}
        }
        // console.log(this.entityForm, id, 111)
      } catch (error) {
      }
    },
    handleActive(e) {
      this.entityForm.activeInertiaRatio = (e / (100 - e)).toFixed(2)
    },
    handleVdaf(e) {
      if (this.entityForm.cleanAd) {
        this.entityForm.cleanVd = ((e * (100 - this.entityForm.cleanAd)) / 100).toFixed(2)
      } else {
        this.entityForm.cleanVd = e
      }
    },
    handleAd(e) {
      if (this.entityForm.cleanVdaf) {
        this.entityForm.cleanVd = ((this.entityForm.cleanVdaf * (100 - e)) / 100).toFixed(2)
      } else {
        this.entityForm.cleanVd = this.entityForm.cleanVdaf
      }
    },
    handleVd(e) {
      if (this.entityForm.cleanAd) {
        this.entityForm.cleanVdaf = ((100 * e) / (100 - this.entityForm.cleanAd)).toFixed(2)
      } else {
        this.entityForm.cleanVdaf = e
      }
    },
    changeRate() {
      this.entityForm.rate = 0
      this.entityForm.coalRockList.map((item) => {
        if (item.proportion) {
          this.entityForm.rate = Number(item.proportion) + this.entityForm.rate
        }
      })
    },
    /*
     * 数值范围验证器
     * */
    RangerValidate(rules, value, cb) {
      if (+value < 0) {
        cb(new Error('数值不能小于0'))
      } else if (+value > 100) {
        cb(new Error('数值不能大于100'))
      } else {
        cb()
      }
    },
    handleClose(type = '', isSave = false) {
      if (type === 'full') {
        this.dialogFormVisible = false
        this.chartVisible = false
        this.entityForm = {...entityForm}
        this.isChangeCoal = false
        this.$emit('closeVisible', {isSave: isSave})
      } else {
        this.dialogVisible = false
        this.position = {}
        this.isChangeCoal = false
      }
    },
    handleSubmitPosition() {
      if (Object.keys(this.position).length === 0) {
        return
      }
      this.$set(this.entityForm, 'longitude', this.position.lng)
      this.$set(this.entityForm, 'latitude', this.position.lat)
      this.handleClose()
    },
    /**
     * 获取煤灰成份
     */
    async handleCoalAsh() {
      const location = {filter_EQS_area: this.entityForm.area}
      const res = await Func.fetch(coalAshList, location)
      if (res.data.records.length > 0) {
        delete res.data.records[0].id
        // 将煤灰成分值转换为整数
        const ashData = { ...res.data.records[0] };
        const ashFields = ['comSiO2', 'comAl2O3', 'comFe2O3', 'comCaO', 'comMgO', 'comNa2O', 'comK2O', 'comTiO2', 'comP2O5', 'comSO3'];
        
        ashFields.forEach(field => {
          if (ashData[field] !== undefined && ashData[field] !== null && ashData[field] !== '') {
            ashData[field] = Math.round(parseFloat(ashData[field]));
          }
        });
        
        this.entityForm = { ...this.entityForm, ...ashData };
      } else {
        this.$message({
          showClose: true,
          message: '无灰成分参考数据，请化验',
          type: 'error'
        })
      }
    },
    /**
     * 关闭煤灰成分弹框
     * @param done
     */
    handleCoalClose(done) {
      done()
    }
  },
  watch: {
    addVisible(v) {
      this.visible = v
    },
    'details.id'(v) {
      if (v) {
        this.getEntityForm()
      } else {
        this.getRangerList()
      }
    },

    details: {
      handler(value) {
        // console.log(value, 'v')
      },
      deep: true,
      immediate: true
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/router-page.scss';

.add {
  ::v-deep .el-dialog__body {
    padding: 0 35px;
    height: 80vh;
  }
}

::v-deep .el-form-item__label {
  font-weight: 400;
}

::v-deep .indicators .title {
  display: block;
}

.textarea {
  ::v-deep .el-form-item__content {
    margin-left: 0 !important;
  }
}

.title-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.title_ad {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.el-col-6 {
  display: flex;
  justify-content: flex-start;
}

.saveBtn {
  display: flex;
  justify-content: flex-end;
}

.dictSelected {
  max-width: 100%;

  ::v-deep .el-input__inner {
    max-width: 100%;
  }
}

.chart-table {
  width: 80%;
  margin: 0 auto;
  margin-bottom: 10px;
  // display: flex;
  // justify-content: center;
}
</style>
